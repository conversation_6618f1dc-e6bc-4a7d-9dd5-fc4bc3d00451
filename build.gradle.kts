plugins {
    kotlin("jvm") version "2.1.20"
    application
}

group = "org.example"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
}

dependencies {
    implementation(fileTree("lib") {
        include("*.jar")
    })
    implementation("dk.dma.enav:enav-model:0.6")
    implementation("dk.dma.commons:dma-commons-util:0.5")
    implementation("net.jcip:jcip-annotations:1.0")
    implementation("org.slf4j:slf4j-api:2.0.9")         // SLF4J API
    implementation("ch.qos.logback:logback-classic:1.4.11") // Logback 作为实现

    implementation("jakarta.xml.bind:jakarta.xml.bind-api:3.0.1")
    implementation("org.glassfish.jaxb:jaxb-runtime:3.0.1")

    implementation("org.antlr:antlr4-runtime:4.13.1")
    implementation("org.reflections:reflections:0.10.2")

    implementation("org.jetbrains.kotlin:kotlin-reflect")

    implementation("io.ktor:ktor-server-netty:3.0.2")   // 根据Kotlin版本选择合适Ktor版本
    implementation("io.ktor:ktor-server-core:3.0.2")
    implementation("io.ktor:ktor-server-call-logging:2.3.7")
    implementation("io.ktor:ktor-server-content-negotiation:3.0.2")
    implementation("ch.qos.logback:logback-classic:1.5.13")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("io.ktor:ktor-serialization-gson:3.0.2")
    implementation("io.ktor:ktor-serialization-jackson:3.0.2")
    implementation("io.ktor:ktor-server-cors:3.0.2")
    implementation("io.ktor:ktor-server-forwarded-header:3.0.2") // OkHttp 最新稳定版

    testImplementation(kotlin("test"))
}

tasks.test {
    useJUnitPlatform()
}
kotlin {
    jvmToolchain(17)
}

//tasks.withType<Jar> {
//    from(fileTree("lib"))
//}

application {
    mainClass.set("org.example.MainKt") // 注意：Main.kt 对应的 class 是 MainKt
}

tasks.jar {
    manifest {
        attributes(
            "Main-Class" to "org.example.MainKt",
            "Class-Path" to configurations.runtimeClasspath.get()
                .joinToString(" ") { "lib/${it.name}" }
        )
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}


tasks.register<Copy>("copyLibs") {
    from(configurations.runtimeClasspath)
    into("$buildDir/libs/lib")
}

tasks.build {
    dependsOn("copyLibs")
}




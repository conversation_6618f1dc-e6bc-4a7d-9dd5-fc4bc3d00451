import dk.dma.ais.message.AisMessage1
import dk.dma.ais.message.AisMessage10
import dk.dma.ais.message.AisMessage11
import dk.dma.ais.message.AisMessage12
import dk.dma.ais.message.AisMessage13
import dk.dma.ais.message.AisMessage14
import dk.dma.ais.message.AisMessage17
import dk.dma.ais.message.AisMessage18
import dk.dma.ais.message.AisMessage19
import dk.dma.ais.message.AisMessage2
import dk.dma.ais.message.AisMessage21
import dk.dma.ais.message.AisMessage24
import dk.dma.ais.message.AisMessage27
import dk.dma.ais.message.AisMessage3
import dk.dma.ais.message.AisMessage4
import dk.dma.ais.message.AisMessage5
import dk.dma.ais.message.AisMessage6
import dk.dma.ais.message.AisMessage7
import dk.dma.ais.message.AisMessage8
import dk.dma.ais.message.AisMessage9
import org.example.util.MessageUtil.preprocessMessage
import org.junit.jupiter.api.Test
import java.io.File
import kotlin.reflect.full.memberProperties

class SimpleTest {
    @Test
    fun test1() {
        val set = mutableSetOf<String>()
        set.addAll(AisMessage1::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage2::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage3::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage4::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage5::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage6::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage7::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage8::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage9::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage10::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage11::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage12::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage13::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage14::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage17::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage18::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage19::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage21::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage24::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })
        set.addAll(AisMessage27::class.memberProperties.map { "${it.name}, ${it.returnType.classifier}" })

        println(set.size)
        println(set.joinToString("\n"))
    }

    @Test
    fun test2() {
        val set = mutableSetOf<Int>()
        File("E:\\workspace\\2025\\01\\datagram\\datagram250108.txt").bufferedReader().forEachLine { line ->
            if (line.isNotEmpty()) {
                val data = line.split("\t")
                val payload = preprocessMessage(data[1]).joinToString("") { it.payload }
                set.addAll(payload.map {
                    if (it.code == 45)
                        println(data[1])
                    it.code
                })
            }
        }
        println(set.sortedBy { it })
        println(set.sortedBy { it }.map { it.toChar() })
    }
}
package org.example.model.response

class Result {
    var rlt = 0 // 0成功 1失败
    var info = "success"
    var data: Any? = null

    constructor() {
        // empty
    }

    constructor(rlt: Int, info: String) {
        this.rlt = rlt
        this.info = info
    }

    constructor(rlt: Int, info: String, data: Any) {
        this.rlt = rlt
        this.info = info
        this.data = data
    }

    constructor(data: Any?) {
        this.data = data
    }

    companion object {
        val success = Result(0, "success")

        fun getSuccess(data: Any?): Result {
            return Result(data)
        }

        fun getError(info: String): Result {
            return Result(1, info)
        }

        fun getError(info: String, data: Any): Result {
            return Result(1, info, data)
        }

        fun getSuccessInfo(info: String) = Result(0, info)
    }
}
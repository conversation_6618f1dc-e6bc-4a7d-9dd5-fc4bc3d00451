package org.example.model.ais

import org.example.annotations.Bit
import org.example.annotations.Indicator
import org.example.constant.AisConstant.FLAG_INDICATOR


open class AisMessage {
    @Bit(0, 6, "消息ID") var msgId: Int = 0
    @Bit(1, 2, "转发指示符") var repeat: Int = 0
    @Bit(2, 30, "用户ID") var userId: Long = 0L
}

open class AisMessage1: AisMessage() {
    // Implementation for AisMessage1
    @Bit(3, 4) var navigationalStatus: Int = 0
    @Bit(4, 8) var rateOfTurn: Int = 0
    @Bit(5, 10) var speedOverGround: Int = 0
    @Bit(6, 1) var positionAccuracy: Int = 0
    @Bit(7, 28) var longitude: Long = 0L
    @Bit(8, 27) var latitude: Long = 0L
    @Bit(9, 12) var courseOverGround: Int = 0
    @Bit(10, 9) var heading: Int = 0
    @Bit(11, 6) var second: Int = 0
    @Bit(12, 2) var maneuverIndicator: Int = 0
    @Bit(13, 3) var spare: Int = 0
    @Bit(14, 1) var raimFlag: Int = 0
    @Bit(16, 2) var syncState: Int = 0
    @Bit(17, 3) var slotTimeout: Int = 0
    @Bit(18, 14) var subMessage: Int = 0
}

class AisMessage2: AisMessage1() {}

class AisMessage3: AisMessage1() {}

open class AisMessage4: AisMessage() {
    @Bit(3, 14) var utcYear: Int = 0
    @Bit(4, 4) var utcMonth: Int = 0
    @Bit(5, 5) var utcDay: Int = 0
    @Bit(6, 5) var utcHour: Int = 0
    @Bit(7, 6) var utcMinute: Int = 0
    @Bit(8, 6) var utcSecond: Int = 0
    @Bit(9, 1) var posAcc: Int = 0
    @Bit(10, 28) var longitude: Long = 0L
    @Bit(11, 27) var latitude: Long = 0L
    @Bit(12, 4) var posType: Int = 0
    @Bit(13, 1) var transmissionControl: Int = 0
    @Bit(14, 9) var spare: Int = 0
    @Bit(15, 1) var raim: Int = 0
    @Bit(16, 2) var syncState: Int = 0
    @Bit(17, 3) var slotTimeout: Int = 0
    @Bit(18, 14) var subMessage: Int = 0
}

class AisMessage5: AisMessage() {
    // AIS版本指示符
    @Bit(3, 2) var versionIndicator: Int = 0
    // imo
    @Bit(4, 30) var imo: Long = 0L
    // callSign
    @Bit(5, 42) var callSign: String = ""
        get() = field.chunked(6).joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
    // shipName
    @Bit(6, 120) var shipName: String = ""
        get() = field.chunked(6).joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
    // shipType
    @Bit(7, 8) var shipType: Int = 0
    // toBow
    @Bit(8, 9) var toBow: Int = 0
    // toStern
    @Bit(9, 9) var toStern: Int = 0
    // toPort
    @Bit(10, 6) var toPort: Int = 0
    // toStarboard
    @Bit(11, 6) var toStarboard: Int = 0
    // epfd
    @Bit(12, 4) var epfd: Int = 0
    // ETA
    @Bit(13, 20) var eta: Eta = Eta()
    // draught
    @Bit(14, 8) var draught: Int = 0
    // destination
    @Bit(15, 120) var destination: String = ""
        get() = field.chunked(6).joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
    // dte
    @Bit(16, 1) var dte: Int = 0
    // spare
    @Bit(17, 1) var spare: Int = 0
}

class Eta {
    // month
    @Bit(0, 4) var month: Int = 0
    // day
    @Bit(1, 5) var day: Int = 0
    // 时
    @Bit(2, 5) var hour: Int = 0
    // 分
    @Bit(3, 6) var minute: Int = 0
}

class AisMessage6: AisMessage() {
    // 序列编号
    @Bit(3, 2) var sequenceNumber: Int = 0
    // 目的地ID
    @Bit(4, 30) var destinationId: Long = 0L
    // 重发标志
    @Bit(5, 1) var retransmitFlag: Int = 0
    // 备用
    @Bit(6, 1) var spare: Int = 0
    @Bit(7, 0) var binaryData: String = ""
}

open class AisMessage7: AisMessage() {
    // 备用
    @Bit(3, 2) var spare: Int = 0
    // 目的地ID
    @Bit(4, 32) var destinationList: MutableList<AckDestination> = mutableListOf()
}

class AckDestination {
    // 目的地ID
    @Bit(0, 30) var destinationId: Long = 0L
    // 序列表好
    @Bit(1, 2) var sequenceNumber: Int = 0
}

class AisMessage8: AisMessage() {
    @Bit(3, 2) var spare: Int = 0
//    @Bit(4, 10) var dac: Int = 0
//    @Bit(5, 6) var fi: Int = 0
    // 确认请求标志
//    @Bit(6, 1) var requestFlag: Int = 0
    // 文本序列编号
//    @Bit(7, 11) var textSequenceNumber: Int = 0
    // 文本串
    @Bit(4, 0) var binaryData: String = "" // 420 bits for text, adjust as needed
}

class AisMessage9: AisMessage() {
    // 高度（GNSS）
    @Bit(3, 12) var gnssHeight: Int = 0
    // sog
    @Bit(4, 10) var sog: Int = 0
    // 位置准确度
    @Bit(5, 1) var positionAccuracy: Int = 0
    // 经度
    @Bit(6, 28) var longitude: Long = 0L
    // 纬度
    @Bit(7, 27) var latitude: Long = 0L
    // cog
    @Bit(8, 12) var cog: Int = 0
    // 时戳
    @Bit(9, 6) var second: Int = 0
    // 高度传感器
    @Bit(10, 1) var altitudeSensor: Int = 0
    // 备用
    @Bit(11, 7) var spare: Int = 0
    // DTE
    @Bit(12, 1) var dte: Int = 0
    // 备用2
    @Bit(13, 3) var spare2: Int = 0
    // 指配模式标志
    @Bit(14, 1) var assignedModeFlag: Int = 0
    // raim
    @Bit(15, 1) var raimFlag: Int = 0
    // 通信状态选择器标志
    @Bit(16, 1) var commStateSelectorFlag: Int = 0
    // 通信状态
    @Bit(17, 19) var commState: Int = 0
}

/**
 * 消息10：协调世界时/日期询问
 * 在一个台站请求另一个台站提供UTC和日期时应采用该消息。
 */
class AisMessage10: AisMessage() {
    // 信源ID
    @Bit(3, 30) var sourceId: Long = 0L
    // 备用
    @Bit(4, 2) var spare: Int = 0
    // 目的地ID
    @Bit(5, 30) var destinationId: Long = 0L
    // 备用2
    @Bit(6, 2) var spare2: Int = 0
}

/**
 * 消息11：协调世界时/日期应答
 * 参考消息4
 */
class AisMessage11: AisMessage4() {}

/**
 * 消息12：寻址安全相关消息
 * 寻址安全相关消息的长度可变，由安全相关文本的数量决定。长度应在1至5时隙间变化。
 */
class AisMessage12: AisMessage() {
    // 序列编号
    @Bit(3, 2) var sequenceNumber: Int = 0
    // 目的地ID
    @Bit(4, 30) var destinationId: Long = 0L
    // 重发标志
    @Bit(5, 1) var retransmitFlag: Int = 0
    // 备用
    @Bit(6, 1) var spare: Int = 0
    // 安全相关文本
    @Bit(7, 0) var securityRelatedText: String = "" // 420 bits for text, adjust as needed
}

/**
 * 消息13：安全相关确认
 * 参考消息7
 */
class AisMessage13: AisMessage7() {}

/**
 * 消息14：安全相关广播消息
 * 与安全有关的广播消息的长度根据与安全有关的文本的量应可以变化。该长度应在1到5个时隙之间变化。
 *
 * AIS-SART应使用消息14，安全相关文本应为：
 * 1) 对于现行的 SART，文本应为“SART ACTIVE”。
 * 2) 对于SART测试模式，文本应为“SART TEST”。
 * 3) 对于现行的MOB，文本应为“MOB ACTIVE”。
 * 4) 对于MOB测试模式，文本应为“MOB TEST”。
 * 5) 对于现行的EPIRB，文本应为“EPIRB ACTIVE”。
 * 6) 对于EPIRB测试模式，文本应为“EPIRB TEST”
 */
class AisMessage14: AisMessage() {
    // 备用
    @Bit(3, 2) var spare: Int = 0
    // 安全相关文本
    @Bit(4, 0) var securityRelatedText: String = ""
}

/**
 * 消息15：询问
 * 该消息不同于对UTC 和数据的请求，它应用于通过TDMA（不是DSC）VHF数据链路的询问。其响应在收到询问后应在该信道上发送。
 */
class AisMessage15: AisMessage() {
    // 备用
    @Bit(3, 2) var spare: Int = 0
    // 目的地ID 1
    @Bit(4, 30) var destinationId1: Long = 0L
    // 消息ID 1.1
    @Bit(5, 6) var messageId1: Int = 0
    // 时隙偏置 1.1
    @Bit(6, 10) var slotOffset1: Int = 0
    // 备用
    @Bit(7, 2) var spare1: Int = 0
    // 消息ID 1.2
    @Bit(8, 6) var messageId2: Int = 0
    // 时隙偏置 1.2
    @Bit(9, 10) var slotOffset2: Int = 0
    // 备用2
    @Bit(10, 2) var spare2: Int = 0
    // 目的地ID 2
    @Bit(11, 30) var destinationId2: Long = 0L
    // 消息ID 2.1
    @Bit(12, 6) var messageId3: Int = 0
    // 时隙偏置 2.1
    @Bit(13, 10) var slotOffset3: Int = 0
    // 备用3
    @Bit(14, 2) var spare3: Int = 0
}

/**
 * 消息16：指配模式命令
 *
 * 指配命令由作为主控实体工作的基站发送。可以给其他台站指配一种与当前所用不同的发送计划。如果给某个台站指配了计划，它也进入指配模式。
 * 可以同时指配两个台站的计划。
 * 在接收一个指配计划时，台站可给该指配附加一个超时标记，可随机在第一次发送之后4至8 min间选择。
 * 当一个A类船载移动AIS台接收到一个指配，它应回复或者是指配的报告频次或者是作为结果的报告频次（当采用了时隙指配）或者是自主确定的报告频次（见附件2的第4.3.1节），取其中最大的。即使该A类船载移动AIS台回复了一个较高的自主确定的报告频次，它也应指出它所处的指配模式（通过采用适当的消息）。
 * 注1 – 指配台站应监测移动台的发送以确定移动台何时超时。
 * 对于指配设置的限制见附件2的表。
 * 由基站采用传输时隙指配传输的消息16应考虑将传输放在已经由基站的FATDMA（消息20）事先保留的时隙中。
 * 如果需要继续指配，在先前指配的最后时帧开始之前应发送新的指配。
 */
class AisMessage16: AisMessage() {
    // 备用
    @Bit(3, 2) var spare: Int = 0
    // 目的地ID A
    @Bit(4, 30) var destinationId1: Long = 0L
    // 偏置 A
    @Bit(5, 12) var slotOffset1: Int = 0
    // 增量 A
    @Bit(6, 10) var increment1: Int = 0
    // 目的地ID B
    @Bit(7, 30) var destinationId2: Long = 0L
    // 偏置 B
    @Bit(8, 12) var slotOffset2: Int = 0
    // 增量 B
    @Bit(9, 10) var increment2: Int = 0
    // 备用2
    @Bit(10, 0) var spare2: Int = 0
}

/**
 * 消息17：全球导航卫星系统广播二进制消息
 * 该消息应由基站发送，该基站与DGNSS参考源相连，其设备配置适于向接收台站提供DGNSS数据。数据内容应符合ITU-R M.823建议书，但前置码和奇偶格式编排除外。
 */
class AisMessage17: AisMessage() {
    // 备用
    @Bit(3, 2) var spare: Int = 0
    // 经度
    @Bit(4, 18) var longitude: Long = 0L
    // 纬度
    @Bit(5, 17) var latitude: Long = 0L
    // 备用
    @Bit(6, 5) var spare2: Int = 0
    // 二进制数据
    @Bit(7, 0) var binaryData: String = "" // 420 bits for binary data, adjust as needed
}

/**
 * 消息18：标准的B类设备位置报告
 * 标准的B类设备位置报告应定期自主生成，而消息1、2或3只用于B类船载移动设备。报告间隔的默认值应为附件1的表2给出的值，除非接收16或23另行规定；并取决于当前的SOG和导航状态标志设置。
 */
class AisMessage18: AisMessage() {
    // 备用
    @Bit(3, 8) var spare: Int = 0
    // sog
    @Bit(4, 10) var sog: Int = 0
    // 位置准确度
    @Bit(5, 1) var positionAccuracy: Int = 0
    // 经度
    @Bit(6, 28) var longitude: Long = 0L
    // 纬度
    @Bit(7, 27) var latitude: Long = 0L
    // cog
    @Bit(8, 12) var cog: Int = 0
    // heading
    @Bit(9, 9) var heading: Int = 0
    // second
    @Bit(10, 6) var second: Int = 0
    // spare2
    @Bit(11, 4) var spare2: Int = 0
    // B类装置标志
    @Bit(12, 1) var classBDeviceFlag: Int = 0
    // B类显示器标志
    @Bit(13, 1) var classBDisplayFlag: Int = 0
    // B类DSC标志
    @Bit(14, 1) var classBDscFlag: Int = 0
    // B类带宽标志
    @Bit(15, 1) var classBBandwidthFlag: Int = 0
    // B类消息22标志
    @Bit(16, 1) var classBMessage22Flag: Int = 0
    // 模式标志
    @Bit(17, 1) var modeFlag: Int = 0
    // RAIM标志
    @Bit(18, 1) var raimFlag: Int = 0
    // 通信状态选择器标志
    @Bit(19, 1) var commStateSelectorFlag: Int = 0
    // 通信状态
    @Bit(20, 2) var syncState: Int = 0
    @Bit(21, 3) var slotTimeout: Int = 0
    @Bit(22, 14) var subMessage: Int = 0
}

class AisMessage19: AisMessage() {
    @Bit(3, 8) var spare: Int = 0
    @Bit(4, 10) var sog: Int = 0
    // 位置准确度
    @Bit(5, 1) var positionAccuracy: Int = 0
    // 经度
    @Bit(6, 28) var longitude: Long = 0L
    // 纬度
    @Bit(7, 27) var latitude: Long = 0L
    @Bit(8, 12) var cog: Int = 0
    @Bit(9, 9) var heading: Int = 0
    @Bit(10, 6) var second: Int = 0
    @Bit(11, 4) var spare2: Int = 0
    // 名称
    @Bit(12, 120) var name: String = ""
        get() = field.chunked(6).joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
    @Bit(13, 8) var shipType: Int = 0
    @Bit(14, 9) var toBow: Int = 0
    @Bit(15, 9) var toStern: Int = 0
    @Bit(16, 6) var toPort: Int = 0
    @Bit(17, 6) var toStarboard: Int = 0
    @Bit(18, 4) var epfd: Int = 0
    @Bit(19, 1) var raimFlag: Int = 0
    @Bit(20, 1) var dte: Int = 0
    @Bit(21, 1) var syncState: Int = 0
    @Bit(22, 4) var spare3: Int = 0
}

/**
 * 消息20：数据链路管理消息
 *
 *  该消息应由基站用于提前公布一个或多个基站的固定指配计划（FATDMA），该消息应按照需要的次数重复。系统可借此为基站提供很高的完整性。这一点对于某些区域特别重要，在这些区域内有若干基站位置相邻，而移动台则在这些不同区域间漫游。移动台不能自主划分这些预留时隙。
 * 在发生超时前，在120海里内[28]移动台则应保留这些时隙，用于基站的发送。每发送一次消息20，基站应刷新一次超时值，以便移动台终止为基站使用时隙而做的保留（参考附件2的第3.3.1.2节）。
 * 偏置数目、时隙数目、超时和增量这几个参数应看做一个整体，如果规定了一个参数，该整体中的其他参数也应规定。偏置数目参数应指明从收到消息20的时隙到要保留的第一时隙之间的偏置。时隙数目参数应指明从要保留的第一个时隙算起的要保留的连续时隙数目。由此规定了一个预留码块。
 * 该预留码块应不超过5时隙。参数增量应指明各预留码块第一时隙间的时隙数目。增量为零表明每帧一个预留码块。建议用于增量的各值为：2，3，5，6，9，10，15，18，25，30，45，50，75，90，125，150，225，250，375，450，750或1125。使用这些值中的一个可保证在每帧内的对称时隙预留。该消息仅适用于发送该消息的频道。
 * 如果遇到询问情况且数据链路管理信息不可用，则仅应发送偏置数目1、时隙数目1、超时1和增量1。这些字段均应置为零。
 */
class AisMessage20: AisMessage() {
    // 备用
    @Bit(3, 2) var spare: Int = 0
}

/**
 * 消息21：助航设备报告
 * 该消息应由助航（AtoN）AIS台使用。该台可以安装在助航设备上，或者在AtoN台站的功能集成到一个固定台站时，该消息也可以由该固定台站发送。该消息应以每三（3）分钟一次的频次发送，或通过VHF数据链路上的指配模式命令或通过按照外部命令指配报告频次。该消息占用的时隙应不超过两个。
 */
class AisMessage21: AisMessage() {
    // 助航设备类型
    @Bit(3, 5) var aidType: Int = 0
    // 助航设备名称
    @Bit(4, 120) var aidName: String = ""
        get() = field.chunked(6).joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
    // 位置准确度
    @Bit(5, 1) var positionAccuracy: Int = 0
    // 经度
    @Bit(6, 28) var longitude: Long = 0L
    // 纬度
    @Bit(7, 27) var latitude: Long = 0L
    // 尺寸/位置参考点
    @Bit(8, 30) var dimensionPositionReference: Int = 0
    // 电子定位装置类型
    @Bit(9, 4) var electronicPositionFixingDeviceType: Int = 0
    // second
    @Bit(10, 6) var second: Int = 0
    // 偏置位置指示符
    @Bit(11, 1) var offsetPositionIndicator: Int = 0
    // AtoN状态
    @Bit(12, 8) var aidStatus: Int = 0
    // raim
    @Bit(13, 1) var raimFlag: Int = 0
    // 虚拟AtoN标志
    @Bit(14, 1) var virtualAidFlag: Int = 0
    // assignedModeFlag
    @Bit(15, 1) var assignedModeFlag: Int = 0
    // spare
    @Bit(16, 1) var spare: Int = 0
    // 助航设备名称扩展
    @Bit(17, 0) var aidNameExtension: String = ""
        get() = field.chunked(6).filter { it.length != 6 }.joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
}

/**
 * 消息22：信道管理
 *
 * 该消息应由基站（作为一个广播消息）发送，为该消息指配的地理地区给出VHF数据链路参数并应配有消息4传输，以在120海里内评估消息。由该消息指配的地理地区应符合附件2第4.1节的规定。另外，该消息也可由基站（作为寻址消息）使用，以命令单独的AIS移动台采用特定的VHF数据链路参数。如果遇到询问情况且被询问的基站没有采取信道管理措施，则应发送“不可用”和/或国际默认设置值（见附件2的第4.1节）。
 */
class AisMessage22: AisMessage() {
    // 备用
    @Bit(3, 2) var spare: Int = 0
    // 信道A
    @Bit(4, 12) var channelA: Int = 0
    // 信道B
    @Bit(5, 12) var channelB: Int = 0
    // Tx/Rx模式
    @Bit(6, 4) var txRxMode: Int = 0
    // 功率
    @Bit(7, 1) var power: Int = 0
    // 经度1
    @Bit(8, 18) var longitude1: Long = 0L
    // 纬度1
    @Bit(9, 17) var latitude1: Long = 0L
    // 经度2
    @Bit(10, 18) var longitude2: Long = 0L
    // 纬度2
    @Bit(11, 17) var latitude2: Long = 0L
    // 寻址或广播消息指示符
    @Bit(12, 1) var addressingOrBroadcastIndicator: Int = 0
    // 信道A带宽
    @Bit(13, 1) var channelABandwidth: Int = 0
    // 信道B带宽
    @Bit(14, 1) var channelBBandwidth: Int = 0
    // 切换区范围
    @Bit(15, 3) var switchingAreaRange: Int = 0
    // 备用2
    @Bit(16, 23) var spare2: Int = 0
}

/**
 * 消息23：群组指配命令
 *
 *  群组指配命令是当基站作为主控实体工作时由其发送的（见附件7的第*******.2节及本附件的第3.20节）。该消息在规定的区域内并通过“船只和货物类型”，或是通过“台站类型”选择适用于移动台。它控制着移动台的下述工作参数：
 * – 发送/接收模式；
 * – 报告时间；以及
 * – 寂静时间。
 * 应采用台站类型10来定义A类和B类“SO”移动站控制消息27传输的基站覆盖区。当台站类型为10时，仅适用纬度、经度字段，忽略所有其他字段。在从同一基站（相同MMSI）最后一次收到控制消息4之后的三分钟内，该信息是相关的。
 */
class AisMessage23: AisMessage() {
    // 备用
    @Bit(3, 2) var spare: Int = 0
    // 经度1
    @Bit(4, 18) var longitude1: Long = 0L
    // 纬度1
    @Bit(5, 17) var latitude1: Long = 0L
    // 经度2
    @Bit(6, 18) var longitude2: Long = 0L
    // 纬度2
    @Bit(7, 17) var latitude2: Long = 0L
    // 台站类型
    @Bit(8, 4) var stationType: Int = 0
    // 船舶类型和货物类型
    @Bit(9, 8) var shipAndCargoType: Int = 0
    // 备用2
    @Bit(10, 22) var spare2: Int = 0
    // Tx/Rx模式
    @Bit(11, 2) var txRxMode: Int = 0
    // 报告间隔
    @Bit(12, 4) var reportInterval: Int = 0
    // 寂静时间
    @Bit(13, 4) var quietTime: Int = 0
    // 备用3
    @Bit(14, 6) var spare3: Int = 0
}

/**
 * 消息24：静态数据报告
 *
 * 支持消息24的A部分的设备须每6分钟变更频道传输一次。
 * 消息24的A部分可由任何AIS台用于将某个名称与MMSI关联。
 * 消息24的A部分和B部分应由B类“CS”和B类“SO”船载移动设备每六分钟发送一次。该消息由两部分组成。消息24B应在消息24A之后的1 min内发送。
 * 当用于电子定位装置的类型的船舶尺寸/位置参考参数值发生变化时，B类“CS”和B类“SO”应传输消息24B。
 * 当要求B类“CS”或B类“SO”传输消息24时，AIS台应以A部分和B部分响应。
 * 当要求A类传输消息24时，AIS台应以B部分响应，该部分可能只包括供应商ID。
 */
open class AisMessage24: AisMessage() {
    // 部分编号
    @Bit(3, 2) var partNumber: Int = 0
}

class AisMessage24A: AisMessage24() {
    // 名称
    @Bit(4, 120) var name: String = ""
        get() = field.chunked(6).joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
}

class AisMessage24B: AisMessage24() {
    // 船舶类型和货物类型
    @Bit(4, 8) var shipAndCargoType: Int = 0
    // 供应商ID
    @Bit(5, 42) var vendorId: String = ""
        get() = field.chunked(6).joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
    // 呼号
    @Bit(6, 42) var callSign: String = ""
        get() = field.chunked(6).joinToString("") {
            if (it.toInt(2) < 31) {
                (it.toInt(2) + 64).toChar().toString()
            } else {
                it.toInt(2).toChar().toString()
            }
        }
    // 船舶大小/位置参考
    // toBow
    @Bit(7, 9) var toBow: Int = 0
    // toStern
    @Bit(8, 9) var toStern: Int = 0
    // toPort
    @Bit(9, 6) var toPort: Int = 0
    // toStarboard
    @Bit(10, 6) var toStarboard: Int = 0
    // 电子定位装置类型
    @Bit(11, 4) var electronicPositionFixingDeviceType: Int = 0
    // 备用
    @Bit(12, 2) var spare: Int = 0
}

/**
 * 消息25：单时隙二进制消息
 *
 * 该消息主要用于简短而且少量的数据传输。单时隙二进制消息根据其内容的编码方法及广播或寻址的目的地指示，可容纳最多128的数据比特。长度不应超过一个时隙。见附件5的第2.1节中的应用标识符。
 * 该消息不应通过消息7或13来确认。
 */
class AisMessage25: AisMessage() {
    @Bit(3, 1) var destinationIdIndicator: Int = 0
    // 二进制数据标记
    @Bit(4, 1) var binaryDataFlag: Int = 0
    @Bit(5, 30) @Indicator("destinationIdIndicator", FLAG_INDICATOR) var destinationId: Long = 0L
    @Bit(6, 2) @Indicator("destinationIdIndicator", FLAG_INDICATOR) var spare: Int = 0
    // 二进制数据
    @Bit(7, 0) var binaryData: String = ""
//    @Bit(7, 10) var dac: Int = 0
//    @Bit(8, 6) var fi: Int = 0
//    // 文本序列编号
////    @Bit(9, 11) var textSequenceNumber: Int = 0
//    // 文本串
//    @Bit(9, 0) var payload: String = "" // 420 bits for text, adjust as needed
}

/**
 * 消息26：带有通信状态的多时隙二进制消息
 *
 * 该消息的主要目的是通过应用SOTDMA或ITDMA接入方式预定二进制数据传输。该多时隙二进制消息取决于对内容的编码方法以及广播或寻址的目的地指示，可包含最多1 004个数据比特（使用5个时隙）。见附件5的第2.1节中的应用标识符。
 * 该消息不应通过消息息7或13来确认。
 */
class AisMessage26: AisMessage() {
    @Bit(3, 1) var destinationIdIndicator: Int = 0
    // 二进制数据标记
    @Bit(4, 1) var binaryDataFlag: Int = 0
    @Bit(5, 30) @Indicator("destinationIdIndicator", FLAG_INDICATOR) var destinationId: Long = 0L
    @Bit(6, 2) @Indicator("destinationIdIndicator", FLAG_INDICATOR) var spare: Int = 0
    // 二进制数据
    @Bit(7, 0) var binaryData: String = ""
//    @Bit(7, 10) var dac: Int = 0
//    @Bit(8, 6) var fi: Int = 0
    // 文本序列编号
//    @Bit(9, 11) var textSequenceNumber: Int = 0
    // 文本串
//    @Bit(9, 0) var payload: String = ""
//    @Bit(11, 2) var syncState: Int = 0
//    @Bit(12, 3) var slotTimeout: Int = 0
//    @Bit(13, 14) var subMessage: Int = 0
}

/**
 * 消息27：远距离自动识别系统广播消息
 *
 * 此消息主要是用于远距离的AIS A类和B类“SO”装备的船只的检测（通常由卫星进行）。此消息具有与消息1、2和3类似的内容，但比特总数已被压缩，以便增加与远距离检测相关的传播延迟。关于远距离应用的详细内容参见附件4。
 */
class AisMessage27: AisMessage() {
    // 位置准确度
    @Bit(3, 1) var positionAccuracy: Int = 0
    // raim
    @Bit(4, 1) var raimFlag: Int = 0
    // 航行状态
    @Bit(5, 4) var navigationalStatus: Int = 0
    // 经度
    @Bit(6, 18) var longitude: Long = 0L
    // 纬度
    @Bit(7, 17) var latitude: Long = 0L
    // 航速
    @Bit(8, 6) var speedOverGround: Int = 0
    // 航向
    @Bit(9, 9) var courseOverGround: Int = 0
    // 位置等待时间
    @Bit(10, 1) var positionWaitTime: Int = 0
    // 备用
    @Bit(11, 1) var spare: Int = 0
}
package org.example.model.ais

import dk.dma.ais.binary.BinArray
import dk.dma.ais.message.AisMessage
import dk.dma.ais.message.AisPosition
import dk.dma.ais.message.binary.AisApplicationMessage
import dk.dma.ais.sentence.Vdm
import org.example.annotations.Order
import java.util.Date
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField

class PrototypeAisMessage {

    // == A. 核心标识信息 ==
    @Order(0, "消息ID")
    var msgId: Int? = null

    @Order(1, "重复次数")
    var repeat: Int? = null

    @Order(2, "用户ID (MMSI)")
    var userId: Int? = null

    @Order(3, "IMO编号")
    var imo: Long? = null

    @Order(4, "版本号")
    var version: Int? = null

    @Order(5, "消息类型")
    var messageType: Int? = null

    @Order(6, "消息内容")
    var message: String? = null


    // == B. 位置信息 ==
    @Order(10, "纬度 (1/1000000 分度)")
    var lat: Int? = null

    @Order(11, "经度 (1/1000000 分度)")
    var lon: Int? = null

    @Order(12, "AIS定位对象")
    var pos: AisPosition? = null

    @Order(13, "位置精度")
    var posAcc: Int? = null

    @Order(14, "地速 (1/10 节)")
    var sog: Int? = null

    @Order(15, "对地航向 (1/10度)")
    var cog: Int? = null

    @Order(16, "真实航向 (度)")
    var trueHeading: Int? = null

    @Order(17, "航向变化率 (度/分钟)")
    var rot: Int? = null

    @Order(18, "GNSS定位状态")
    var gnssPosStatus: Int? = null

    @Order(19, "定位类型")
    var posType: Int? = null


    // == C. 时间相关 ==
    @Order(20, "UTC秒数")
    var utcSec: Int? = null

    @Order(21, "预计到达时间 (ETA, 时间戳)")
    var eta: Long? = null

    @Order(22, "时间计数Z")
    var zCount: Int? = null


    // == D. 船舶基本信息 ==
    @Order(30, "船名")
    var name: String? = null

    @Order(31, "船名扩展")
    var nameExt: String? = null

    @Order(32, "呼号")
    var callsign: String? = null

    @Order(33, "船舶类型")
    var shipType: Int? = null

    @Order(34, "吃水深度 (分米)")
    var draught: Int? = null

    @Order(35, "目的地 (编码)")
    var destination: Long? = null

    @Order(36, "目的地 (名称)")
    var dest: String? = null


    // == E. 船舶尺寸 ==
    @Order(40, "船头尺寸 (米)")
    var dimBow: Int? = null

    @Order(41, "船尾尺寸 (米)")
    var dimStern: Int? = null

    @Order(42, "左舷尺寸 (米)")
    var dimPort: Int? = null

    @Order(43, "右舷尺寸 (米)")
    var dimStarboard: Int? = null


    // == F. AIS通信状态与同步 ==
    @Order(50, "通信状态")
    var commState: Int? = null

    @Order(51, "通信状态选择标志")
    var commStateSelectorFlag: Int? = null

    @Order(52, "同步状态")
    var syncState: Int? = null

    @Order(53, "时隙超时")
    var slotTimeout: Int? = null

    @Order(54, "时隙增量")
    var slotIncrement: Int? = null

    @Order(55, "时隙数量")
    var numSlots: Int? = null

    @Order(56, "保持标志")
    var keep: Int? = null


    // == G. 特殊功能标志 ==
    @Order(60, "导航状态")
    var navStatus: Int? = null

    @Order(61, "特殊管理指示符")
    var specialManIndicator: Int? = null

    @Order(62, "RAIM标志")
    var raim: Int? = null

    @Order(63, "RAIM标志 (备用)")
    var raimFlag: Int? = null

    @Order(64, "模式标志")
    var modeFlag: Int? = null

    @Order(65, "数据终止指示符")
    var dte: Int? = null

    @Order(66, "重传标志")
    var retransmit: Int? = null

    @Order(67, "分配标志")
    var assigned: Int? = null


    // == H. 航标相关 ==
    @Order(70, "航标类型")
    var atonType: Int? = null

    @Order(71, "位置偏差标志")
    var offPosition: Int? = null

    @Order(72, "虚拟航标标志")
    var virtual: Int? = null

    @Order(73, "区域相关")
    var regional: Int? = null


    // == I. Class B 船舶特有 ==
    @Order(80, "Class B 单元标志")
    var classBUnitFlag: Int? = null

    @Order(81, "Class B 显示标志")
    var classBDisplayFlag: Int? = null

    @Order(82, "Class B DSC 标志")
    var classBDscFlag: Int? = null

    @Order(83, "Class B 频段标志")
    var classBBandFlag: Int? = null

    @Order(84, "Class B Msg22 标志")
    var classBMsg22Flag: Int? = null


    // == J. 附加数据字段 ==
    @Order(90, "VDM对象")
    var vdm: Vdm? = null

    @Order(91, "子消息")
    var subMessage: Int? = null

    @Order(92, "序列号")
    var seqNum: Int? = null

    @Order(93, "二进制数据")
    var data: BinArray? = null

    @Order(94, "应用消息")
    var appMessage: AisApplicationMessage? = null

    @Order(95, "DAC")
    var dac: Int? = null

    @Order(96, "FI")
    var fi: Int? = null


    // == K. 目的地序列 ==
    @Order(100, "目的地1")
    var dest1: Long? = null

    @Order(101, "序列号1")
    var seq1: Int? = null

    @Order(102, "目的地2")
    var dest2: Long? = null

    @Order(103, "序列号2")
    var seq2: Int? = null

    @Order(104, "目的地3")
    var dest3: Long? = null

    @Order(105, "序列号3")
    var seq3: Int? = null

    @Order(106, "目的地4")
    var dest4: Long? = null

    @Order(107, "序列号4")
    var seq4: Int? = null


    // == L. 其他与扩展 ==
    @Order(110, "高度")
    var altitude: Int? = null

    @Order(111, "区域保留")
    var regionalReserved: Int? = null

    @Order(112, "备用字段")
    var spare: Int? = null

    @Order(113, "备用字段1")
    var spare1: Int? = null

    @Order(114, "备用字段2")
    var spare2: Int? = null

    @Order(115, "备用字段3")
    var spare3: Int? = null

    @Order(116, "用户ID后的备用字段")
    var spareAfterUserId: Int? = null


    // == M. 站点/数据完整性 ==
    @Order(120, "站点ID")
    var stationId: Int? = null

    @Order(121, "数据字计数")
    var dataWordCount: Int? = null

    @Order(122, "数据字数组")
    var dataWords: IntArray? = null

    @Order(123, "健康状态")
    var health: Int? = null


    // == N. 厂商识别 ==
    @Order(130, "零件号")
    var partNumber: Int? = null

    @Order(131, "厂商ID")
    var vendorId: String? = null

    constructor()

    constructor(aisMessage: AisMessage) {
        val messageProperties = aisMessage::class.memberProperties
        val properties = this::class.memberProperties
        properties.forEach { property ->
            if (property is KMutableProperty<*>) {
                property.isAccessible = true
                messageProperties.find { it.name == property.name }?.let { messageProperty ->
                    messageProperty.isAccessible = true
                    property.setter.call(this, messageProperty.getter.call(aisMessage))
                }
            }
        }
    }

    fun toMap(): Map<String, Any> {
        return this::class.java.declaredFields.filter { it.get(this) != null }.associate { it.name to it.get(this) }
    }

    fun toDescription(): String {
        val stringBuilder: StringBuilder = StringBuilder()
        val propertyMap = this::class.memberProperties.associateBy {
            it.javaField?.getAnnotation(Order::class.java)
        }.toList().filter { it.second.getter.call(this) != null }.sortedBy {
            it.first?.index ?: 0
        }

        propertyMap.filter { (it.first?.index ?: 0) in 0..9 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("核心标识信息：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 10..19 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n位置信息：\n")
                list.forEach {
                    stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n")
                    if (it.second.name == "pos") {
                        val pos = it.second.getter.call(this) as AisPosition
                        stringBuilder.append("经度=${pos.longitudeDouble}\n")
                        stringBuilder.append("纬度=${pos.latitudeDouble}\n")
                    }
                }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 20..29 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n时间信息：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 30..39 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n船舶基本信息：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 40..49 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n船舶尺寸信息：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 50..59 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n通信状态与同步信息：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 60..69 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n特殊功能标志信息：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 70..79 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n航标相关：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 80..89 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\nClass B 船舶特有：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 90..99 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n附加数据字段：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 100..109 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n目的地序列：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 110..119 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n其他与扩展：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 120..129 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n站点/数据完整性：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        propertyMap.filter { (it.first?.index ?: 0) in 130..139 }.let { list ->
            if (list.isNotEmpty()) {
                stringBuilder.append("\n厂商识别：\n")
                list.forEach { stringBuilder.append("${it.first?.description ?: "未知字段"}=${it.second.getter.call(this)}\n") }
            }
        }

        return stringBuilder.toString()
    }
}

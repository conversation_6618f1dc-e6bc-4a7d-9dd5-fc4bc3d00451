package org.example.model.ais

import org.example.annotations.Order

/**
 * NMEA 0183 结构
 * 1	总段数	2	多段消息的总数
 * 2	当前段编号	1 / 2	当前是第几段
 * 3	序列号	9	多段消息合并标识
 * 4	信道	A / B	AIS 信道 A 或 B
 * 5	Payload	569FS...	实际 AIS 编码内容
 * 6	填充位数 (pad bits)	0 / 2	表示最后一字节中有几位是无效的填充值（用于凑满6位一组）
 * 7	校验和前缀 + 校验和	*01 / *0B	* 后两位是十六进制校验和（Checksum）
 */
class NMEA {
    @Order(0) var header: String = ""
    @Order(1) var totalSegments: String = ""
    @Order(2) var currentSegment: String = ""
    @Order(3) var sequenceNumber: String = ""
    @Order(4) var channel: String = "" // A or B
    @Order(5) var payload: String = ""
    @Order(6) var padBits: String = ""
    @Order(7) var checksum: String = ""

    override fun toString(): String {
        return "$header,$totalSegments,$currentSegment,$sequenceNumber,$channel,$payload,$padBits*$checksum"
    }
}
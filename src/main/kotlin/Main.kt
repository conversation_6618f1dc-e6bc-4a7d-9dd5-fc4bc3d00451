package org.example

import com.fasterxml.jackson.databind.SerializationFeature
import io.ktor.http.*
import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.server.http.content.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.plugins.cors.routing.*
import io.ktor.server.plugins.forwardedheaders.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.example.model.request.AisDecodeRequest
import org.example.util.MessageUtil.decodeSingleMessage

//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
//fun main() {
//    val paramStr = "ID,MMSI,SOG,LONGITUDE,LATITUDE,COG,HEADING,TIMESTAMP,TIMESTAMP,NAME,TYPE,LOA,BM,OFFPOSITION,VIRTUALATON,PART24A,PART24B,CALLSIGN,UTC_YEAR,UTC_MONTH,UTC_DAY,UTC_HOUR,UTC_MINUTE,UTC_SEC,IMO,ETA,DRAUGHT,DEST"
//    val aisPacketParser = AisPacketParser()
//    println(decodeSingleMessage("!ABVDM,1,1,,B,177KQJ5000G?tO`K>RA1wUbN0TKH,0*57"))
//}

fun main() {
    embeddedServer(Netty, host = "0.0.0.0", port = 8086) {
        install(ForwardedHeaders) // WARNING: for security, do not include this if not behind a reverse proxy
        install(XForwardedHeaders) // WARNING: for security, do not include this if not behind a reverse proxy

        install(ContentNegotiation) {
            jackson {
                enable(SerializationFeature.INDENT_OUTPUT)
            }
        }

        install(CORS) {
            anyHost() // ⚠️ 开发阶段使用，生产环境请限制具体域名

            allowMethod(HttpMethod.Get)
            allowMethod(HttpMethod.Post)
            allowMethod(HttpMethod.Put)
            allowMethod(HttpMethod.Delete)

            allowHeader(HttpHeaders.ContentType)
            allowHeader(HttpHeaders.Authorization)

            allowCredentials = true // 如果需要发送 Cookie 或 Header 带身份验证信息
        }

        routing {
            staticResources("/", "static") // 第一个参数是访问路径，第二个是资源文件夹名

            post("/tool-api/ais/decode") {
                try {
                    // 获取查询参数
                    val request = call.receive<AisDecodeRequest>()
                    log.info("接收到的 request 数据: $request")

                    // 调用服务层逻辑
                    call.respond(org.example.model.response.Result.getSuccess(decodeSingleMessage(request.message)))
                } catch (e: Exception) {
                    log.error(e.message)
                    call.respond(org.example.model.response.Result.getError("操作失败: ${e.message}"))
                }
            }

            // 健康检查端点
            get("/health") {
                call.respondText("Hello, MCP!")
            }
        }
    }.start(wait = true)
}
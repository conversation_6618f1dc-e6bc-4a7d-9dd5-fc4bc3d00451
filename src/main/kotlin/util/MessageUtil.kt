package org.example.util

import dk.dma.ais.packet.AisPacket
import dk.dma.ais.packet.AisPacketParser
import kotlinx.coroutines.delay
import org.example.annotations.Bit
import org.example.annotations.Order
import org.example.constant.AisConstant.FLAG_INDICATOR
import org.example.constant.AisConstant.SIZE_INDICATOR
import org.example.model.ais.AisMessage1
import org.example.model.ais.AisMessage19
import org.example.model.ais.AisMessage25
import org.example.model.ais.AisMessage26
import org.example.model.ais.AisMessage27
import org.example.model.ais.AisMessage4
import org.example.model.ais.AisMessage5
import org.example.model.ais.AisMessage8
import org.example.model.ais.NMEA
import org.example.model.ais.PrototypeAisMessage
import org.example.util.MessageUtil.charToSixBit
import org.example.util.MessageUtil.charToSixBit2
import org.example.util.MessageUtil.decodeSingleMessage
import org.example.util.MessageUtil.payloadToSixBit
import org.example.util.MessageUtil.preprocessMessage
import kotlin.math.abs
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.reflect.jvm.javaField
import kotlin.system.measureTimeMillis

object MessageUtil {
    private val aisPacketParser = AisPacketParser()

    fun decodeSingleMessage(message: String): String {
        var result = ""
        var aisPacket: AisPacket? = null
        preprocessMessage(message).filter { it.header.endsWith("VDM") || it.header.endsWith("VDO") }.forEach {
            println(it)
            aisPacket = aisPacketParser.readLine(it.toString())
        }
        aisPacket?.aisMessage?.let { aisMessage ->
            try {
                result = PrototypeAisMessage(aisMessage).toDescription()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return result
    }

    /**
     *
     * this method is used to preprocess the AIS message before decoding.
     * input: !AIVDM,2,1,9,B,569FS3T000009HIWF20P4V1PT4pN0PtpN2222216=8E9:5`bNDB0BH2k,0*01!AIVDM,2,2,9,B,mH8888888888880,2*0B$AIVSI,TEST BS 360,9,015350.856770,1907,-105,24*78
     * result:
     * !AIVDM,2,1,9,B,569FS3T000009HIWF20P4V1PT4pN0PtpN2222216=8E9:5`bNDB0BH2k,0*01
     * !AIVDM,2,2,9,B,mH8888888888880,2*0B
     * !AIVSI,TEST BS 360,9,015350.856770,1907,-105,24*78
     */
    fun preprocessMessage(message: String): MutableList<NMEA> {
        val list = mutableListOf<NMEA>()
        message.split("\n").filter { it.isNotEmpty() }.forEach {
            creatNMEA(list, it)
        }
        return list
    }

    fun creatNMEA(list: MutableList<NMEA>, message: String) {
        val nmea = NMEA()
        val properties = nmea::class.memberProperties.associate {
            it.isAccessible = true
            (it.javaField?.getAnnotation(Order::class.java)?.index ?: 0) to (it as KMutableProperty<*>)
        }

        var index = 0
        var offset = 0
        var length = 0

        message.forEach { char ->
            when (char) {
                ',' -> {
                    val property = properties[index] ?: return@forEach
                    val value = message.substring(offset, offset + length).trim()
                    property.setter.call(nmea, value)
                    index++
                    offset += length + 1 // Move past the comma
                    length = 0 // Reset length for the next segment
                }
                '*' -> {
                    nmea.padBits = message.substring(offset, offset + length).trim()
                    nmea.checksum = message.substring(offset + length + 1, offset + length + 3).trim()
                    list.add(nmea)
                    creatNMEA(list, message.substring(offset + length + 3).trim())
                    return
                }
                else -> {
                    length ++
                }
            }
        }
    }

    fun charToSixBit(char: Char): String {
        val asciiValue = char.code
        return if (asciiValue < 96) {
            (asciiValue - 48).toString(2).padStart(6, '0')
        } else {
            (asciiValue - 56).toString(2).padStart(6, '0')
        }
    }

    fun charToSixBit2(char: Char): String {
        val asciiValue = char.code
        return if (asciiValue < 88) {
            (asciiValue - 48).toString(2).padStart(6, '0')
        } else {
            (asciiValue - 56).toString(2).padStart(6, '0')
        }
    }

    fun payloadToSixBit(payload: String): String {
        return payload.map { charToSixBit(it) }.joinToString("")
    }
}

fun main() {
//    val str = """
//        !ABVDM,1,1,,A,Iwog=FvGiTaVilh9q@,4*3A
//        !AIVDM,1,1,,B,IEKuBt2@>Qd3Ov3ORjOQ:3wGbNt,2*6B
//        !ABVDM,1,1,5,B,IE>`=t1@DpL00;3GR0000000000,2*16
//        !ABVDM,1,1,7,B,IE2`AL@P4V000000F0P4t000000,2*5C
//        !AIVDM,1,1,,B,IPBawu6SdrNOsgII,0*7B
//        !ABVDM,1,1,,A,I58twAhK<F>s0V=VGnM1,0*67
//        !AIVDM,1,1,,A,I@9c=Im7<hw?WpL7IWP0p0tDghL=,0*6B
//        !ABVDM,1,1,,A,IAdWeNHvgDhnwhUsgn?v77>gcKFUsEq2sicIKdCJhwNaUuKA9;;epC1IwP,4*70
//        !AIVDM,1,1,,B,IQsvFpo91@,4*2D
//        !ABVDM,1,1,4,B,I70gwfU6EQui1W9h000ppq1P?87W,0*39
//        !ABVDM,1,1,3,A,IaTU7g5OAep80ndsjKDRfvg9=qG;kc>WFbCHSmv2fPBqLlc5`:6<DA8Hph8@UTnD7KbK;BP78kWSv>eObeB2>meVo@g`,0*70
//        !ABVDM,1,1,7,B,ISqs:qivMdiV?`inT<FEAr100D:D,0*48
//        !ABVDM,1,1,6,A,Is9s:q1vMdhl@8kFNdFTPSA000S:,0*3C
//        !AIVDM,1,1,,B,I2A@6m7=btfcLf94soT,2*70
//        !ABVDM,1,1,,A,ItAwgN?Vl0,4*3E
//        !ABVDM,1,1,,B,IUjI?OD`kMh0ukh,2*4F
//        !ABVDM,1,1,,B,IClnOk9WuogBINe5dRwC<qgAprADCQT,2*2E
//    """.trimIndent()
//    str.split("\n").forEach { split ->
//        val payload = preprocessMessage(split.trimIndent()).joinToString("") { payloadToSixBit(it.payload) }
//        println(payload.length)
//    }
    // 011000 000101 011110 100111 100110 011010 001100 100110 001011 000001 110000 100000 010000 001000 000100 000010 000001 000000 100000 010000 001000 001111 0000
    //
//    println(decodeSingleMessage("!ABVDM,1,1,,B,869G03iW@bT61GaqVS9RhL84210P@8423h,4*41"))
    val message = preprocessMessage("!ABVDM,1,1,,A,K?SwVwwk?@Wh7slV,0*7E".trimIndent()).joinToString("") { it.payload }
    val payload = payloadToSixBit(message)

//    for (i in 88..96) {
//        println(i.toChar())
//    }
    println(message.map { "$it,${it.code},${charToSixBit(it)}" }.joinToString(" "))
    println(message.map { "$it,${it.code},${charToSixBit(it)}" }.joinToString(" "))

    println(payload)
    val aisMessage1 = AisMessage27()
    val properties = aisMessage1::class.memberProperties.associate {
        it.isAccessible = true
        (it.javaField?.getAnnotation(Bit::class.java)?.index ?: 0) to (it as KMutableProperty<*>)
    }

    var offset = 0
    for (index in 0..properties.size) {
        val property = properties[index] ?: continue
        var bit = property.javaField?.getAnnotation(Bit::class.java)?.bit ?: continue

        if (bit <= 0)
            bit = payload.length - offset - abs(bit) // If bit is negative, calculate the remaining length from the offset

        property.javaField?.getAnnotation(org.example.annotations.Indicator::class.java)?.let { indicator ->
            properties.values.find { it.name == indicator.name }?.let { indicatorProperty ->
                indicatorProperty.getter.call(aisMessage1)?.let { indicatorValue ->
                    bit = when (indicator.type) {
                        FLAG_INDICATOR -> if (indicatorValue as Int == 1) bit else 0
                        SIZE_INDICATOR -> indicatorValue as Int
                        else -> throw IllegalArgumentException("Unsupported indicator type: ${indicator.type}")
                    }
                }
            }
        }

        val value = payload.substring(offset, offset + bit)

        when (property.returnType.classifier) {
            String::class -> property.setter.call(aisMessage1, value)
            Int::class -> property.setter.call(aisMessage1, value.toIntOrNull(2) ?: 0)
            Long::class -> property.setter.call(aisMessage1, value.toLongOrNull(2) ?: 0L)
            else -> throw IllegalArgumentException("Unsupported type: ${property.returnType}")
        }

        offset += bit
    }

    println(jacksonObjectMapper.writeValueAsString(aisMessage1))
}

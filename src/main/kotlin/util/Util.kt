package org.example.util

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import dk.dma.ais.message.AisMessage
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

fun AisMessage.toMutableMap(): Map<String, Any> {
    val properties = this::class.memberProperties
    return properties.associate { property ->
        property.isAccessible = true
        property.name to property.getter.call(this)
    }.filterValues { it != null }.mapValues { it.value!! }
}

val jacksonObjectMapper = jacksonObjectMapper()
/* Copyright (c) 2011 Danish Maritime Authority.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dk.dma.ais.configuration.filter;

import jakarta.xml.bind.annotation.XmlRootElement;

import dk.dma.ais.filter.ExpressionFilter;
import dk.dma.ais.filter.IPacketFilter;

/**
 * The type Expression filter configuration.
 */
@XmlRootElement
public class ExpressionFilterConfiguration extends FilterConfiguration {

    private String expression;

    /**
     * Instantiates a new Expression filter configuration.
     */
    public ExpressionFilterConfiguration() {

    }

    /**
     * Gets expression.
     *
     * @return the expression
     */
    public String getExpression() {
        return expression;
    }

    /**
     * Sets expression.
     *
     * @param expression the expression
     */
    public void setExpression(String expression) {
        this.expression = expression;
    }

    @Override
    public IPacketFilter getInstance() {
        return new ExpressionFilter(expression);
    }

}

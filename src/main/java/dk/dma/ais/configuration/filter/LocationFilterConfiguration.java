/* Copyright (c) 2011 Danish Maritime Authority.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dk.dma.ais.configuration.filter;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlTransient;

import dk.dma.ais.configuration.filter.geometry.GeometryConfiguration;
import dk.dma.ais.filter.IPacketFilter;
import dk.dma.ais.filter.LocationFilter;

/**
 * The type Location filter configuration.
 */
@XmlRootElement
public class LocationFilterConfiguration extends FilterConfiguration {

    private List<GeometryConfiguration> geometries = new ArrayList<>();

    /**
     * Instantiates a new Location filter configuration.
     */
    public LocationFilterConfiguration() {

    }

    /**
     * Gets geometries.
     *
     * @return the geometries
     */
    @XmlElement(name = "geometry")
    public List<GeometryConfiguration> getGeometries() {
        return geometries;
    }

    /**
     * Sets geometries.
     *
     * @param geometries the geometries
     */
    public void setGeometries(List<GeometryConfiguration> geometries) {
        this.geometries = geometries;
    }

    @Override
    @XmlTransient
    public IPacketFilter getInstance() {
        LocationFilter locFilter = new LocationFilter();
        for (GeometryConfiguration geo : geometries) {
            locFilter.addFilterGeometry(geo.getPredicate());
        }
        return locFilter;
    }

}

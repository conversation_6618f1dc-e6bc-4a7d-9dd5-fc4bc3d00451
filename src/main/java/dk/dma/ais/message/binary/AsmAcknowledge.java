/* Copyright (c) 2011 Danish Maritime Authority.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dk.dma.ais.message.binary;

import dk.dma.ais.binary.BinArray;
import dk.dma.ais.binary.SixbitEncoder;
import dk.dma.ais.binary.SixbitException;

/**
 * Application acknowledge ASM
 */
public class AsmAcknowledge extends AisApplicationMessage {

    /**
     * DAC code of received FM
     * 
     * Recommended to be spare
     */
    private int receivedDac; // 10 bits
    /**
     * FI code of received FM
     */
    private int receivedFi; // 6 bits
    /**
     * Sequence number in the message being acknowledged as properly received 0 = default (no sequence number) 1-2 047 =
     * sequence number of received FM
     */
    private int textSequenceNum; // 11 bits
    /**
     * 0 = received but AI not available 1 = AI available
     */
    private int aiAvailable; // 1 bit
    /**
     * 0 = unable to respond 1 = reception acknowledged 2 = response to follow 3 = able to respond but currently
     * inhibited 4-7 = spare for future use
     */
    private int aiResponse; // 3 bits
    private int spare; // 49 bits

    /**
     * Instantiates a new Asm acknowledge.
     */
    public AsmAcknowledge() {
        super(1, 5);
    }

    /**
     * Instantiates a new Asm acknowledge.
     *
     * @param binArray the bin array
     * @throws SixbitException the sixbit exception
     */
    public AsmAcknowledge(BinArray binArray) throws SixbitException {
        super(1, 5, binArray);
    }

    @Override
    public void parse(BinArray binArray) throws SixbitException {
        this.receivedDac = (int) binArray.getVal(10);
        this.receivedFi = (int) binArray.getVal(6);
        this.textSequenceNum = (int) binArray.getVal(11);
        this.aiAvailable = (int) binArray.getVal(1);
        this.aiResponse = (int) binArray.getVal(3);
    }

    @Override
    public SixbitEncoder getEncoded() {
        SixbitEncoder encoder = new SixbitEncoder();
        encoder.addVal(receivedDac, 10);
        encoder.addVal(receivedFi, 6);
        encoder.addVal(textSequenceNum, 11);
        encoder.addVal(aiAvailable, 1);
        encoder.addVal(aiResponse, 3);
        encoder.addVal(0, 49);
        return encoder;
    }

    /**
     * Gets received dac.
     *
     * @return the received dac
     */
    public int getReceivedDac() {
        return receivedDac;
    }

    /**
     * Sets received dac.
     *
     * @param receivedDac the received dac
     */
    public void setReceivedDac(int receivedDac) {
        this.receivedDac = receivedDac;
    }

    /**
     * Gets received fi.
     *
     * @return the received fi
     */
    public int getReceivedFi() {
        return receivedFi;
    }

    /**
     * Sets received fi.
     *
     * @param receivedFi the received fi
     */
    public void setReceivedFi(int receivedFi) {
        this.receivedFi = receivedFi;
    }

    /**
     * Gets text sequence num.
     *
     * @return the text sequence num
     */
    public int getTextSequenceNum() {
        return textSequenceNum;
    }

    /**
     * Sets text sequence num.
     *
     * @param textSequenceNum the text sequence num
     */
    public void setTextSequenceNum(int textSequenceNum) {
        this.textSequenceNum = textSequenceNum;
    }

    /**
     * Gets ai available.
     *
     * @return the ai available
     */
    public int getAiAvailable() {
        return aiAvailable;
    }

    /**
     * Sets ai available.
     *
     * @param aiAvailable the ai available
     */
    public void setAiAvailable(int aiAvailable) {
        this.aiAvailable = aiAvailable;
    }

    /**
     * Gets ai response.
     *
     * @return the ai response
     */
    public int getAiResponse() {
        return aiResponse;
    }

    /**
     * Sets ai response.
     *
     * @param aiResponse the ai response
     */
    public void setAiResponse(int aiResponse) {
        this.aiResponse = aiResponse;
    }

    /**
     * Gets spare.
     *
     * @return the spare
     */
    public int getSpare() {
        return spare;
    }

    /**
     * Sets spare.
     *
     * @param spare the spare
     */
    public void setSpare(int spare) {
        this.spare = spare;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append(super.toString());
        builder.append(", aiAvailable=");
        builder.append(aiAvailable);
        builder.append(", aiResponse=");
        builder.append(aiResponse);
        builder.append(", receivedDac=");
        builder.append(receivedDac);
        builder.append(", receivedFi=");
        builder.append(receivedFi);
        builder.append(", spare=");
        builder.append(spare);
        builder.append(", textSequenceNum=");
        builder.append(textSequenceNum);
        builder.append("]");
        return builder.toString();
    }

}

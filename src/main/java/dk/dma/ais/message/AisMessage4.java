/* Copyright (c) 2011 Danish Maritime Authority.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dk.dma.ais.message;

import dk.dma.ais.binary.SixbitException;
import dk.dma.ais.sentence.Vdm;

import java.util.ArrayList;
import java.util.List;

/**
 * AIS message 4
 * <p>
 * Base station report as defined by ITU-R M.1371-4
 */
public class AisMessage4 extends UTCDateResponseMessage {

    /**
     * serialVersionUID.
     */
    private static final long serialVersionUID = 1L;

    /**
     * Instantiates a new Ais message 4.
     */
    public AisMessage4() {
        super(4);
    }

    /**
     * Instantiates a new Ais message 4.
     *
     * @param vdm the vdm
     * @throws AisMessageException the ais message exception
     * @throws SixbitException     the sixbit exception
     */
    public AisMessage4(Vdm vdm) throws AisMessageException, SixbitException {
        super(vdm);
    }

    @Override
    public void acquireRltList(List<String> params, List<String> rltList) {
        super.acquireRltList(params, rltList);
        if (params.contains("LONGITUDE")) {
            rltList.add("\"LONGITUDE\":" + "\"" + this.getPos().getRawLongitude() + "\"");
        }
        if (params.contains("LATITUDE")) {
            rltList.add("\"LATITUDE\":" + "\"" + this.getPos().getRawLatitude() + "\"");
        }
        if (params.contains("UTC_YEAR")) {
            rltList.add("\"UTC_YEAR\":" + "\"" + this.getUtcYear() + "\"");
        }
        if (params.contains("UTC_MONTH")) {
            rltList.add("\"UTC_MONTH\":" + "\"" + this.getUtcMonth() + "\"");
        }
        if (params.contains("UTC_DAY")) {
            rltList.add("\"UTC_DAY\":" + "\"" + this.getUtcDay() + "\"");
        }
        if (params.contains("UTC_HOUR")) {
            rltList.add("\"UTC_HOUR\":" + "\"" + this.getUtcHour() + "\"");
        }
        if (params.contains("UTC_MINUTE")) {
            rltList.add("\"UTC_MINUTE\":" + "\"" + this.getUtcMinute() + "\"");
        }
        if (params.contains("UTC_SEC")) {
            rltList.add("\"UTC_SEC\":" + "\"" + this.getUtcSecond() + "\"");
        }
    }
}

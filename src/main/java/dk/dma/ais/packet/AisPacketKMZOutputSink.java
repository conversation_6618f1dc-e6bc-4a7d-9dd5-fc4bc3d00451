///* Copyright (c) 2011 Danish Maritime Authority.
// *
// * Licensed under the Apache License, Version 2.0 (the "License");
// * you may not use this file except in compliance with the License.
// * You may obtain a copy of the License at
// *
// *     http://www.apache.org/licenses/LICENSE-2.0
// *
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS,
// * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// * See the License for the specific language governing permissions and
// * limitations under the License.
// */
//package dk.dma.ais.packet;
//
//import com.google.common.io.Files;
//import dk.dma.ais.message.NavigationalStatus;
//import dk.dma.ais.message.ShipTypeCargo;
//import dk.dma.ais.message.ShipTypeColor;
//import java.util.function.BiFunction;
//import java.util.function.Predicate;
//import java.util.function.Supplier;
//import net.jcip.annotations.NotThreadSafe;
//
//import java.io.BufferedOutputStream;
//import java.io.File;
//import java.io.IOException;
//import java.io.OutputStream;
//import java.util.zip.ZipEntry;
//import java.util.zip.ZipOutputStream;
//
///**
// * This class receives AisPacket and use them to build a scenario for replay in Google Earth.
// * <p>
// * When the sink is closed it dumps the entire target state to the output stream in KMZ format.
// */
//@NotThreadSafe
//class AisPacketKMZOutputSink extends AisPacketKMLOutputSink {
//
//    /**
//     * Instantiates a new Ais packet kmz output sink.
//     */
//    public AisPacketKMZOutputSink() {
//        super();
//    }
//
//    /**
//     * Create a sink that writes KMZ contents to outputStream - but build the scenario only from
//     * AisPackets which comply with the filter predicate.
//     *
//     * @param filter a filter predicate for pre-filtering of AisPackets.
//     */
//    public AisPacketKMZOutputSink(Predicate<? super AisPacket> filter) {
//        super(filter);
//    }
//
//    /**
//     * Create a sink that writes KMZ contents to outputStream - but build the scenario only from
//     * AisPackets which comply with the filter predicate.
//     *
//     * @param filter                            a filter predicate for pre-filtering of AisPackets before they are passed to the tracker.
//     * @param createSituationFolder             create and populate the 'situation' folder in the generated KML
//     * @param createMovementsFolder             create and populate the 'movements' folder in the generated KML
//     * @param createTracksFolder                create and populate the 'tracks' folder in the generated KML
//     * @param isPrimaryTarget                   Apply primary KMZ styling to targets which are updated by packets that pass this predicate.
//     * @param isSecondaryTarget                 Apply secondary KMZ styling to targets which are updated by packets that pass this predicate.
//     * @param triggerSnapshot                   the trigger snapshot
//     * @param snapshotDescriptionSupplier       the snapshot description supplier
//     * @param movementInterpolationStepSupplier the movement interpolation step supplier
//     * @param iconHrefSupplier                  the icon href supplier
//     */
//    public AisPacketKMZOutputSink(Predicate<? super AisPacket> filter, boolean createSituationFolder, boolean createMovementsFolder, boolean createTracksFolder, Predicate<? super AisPacket> isPrimaryTarget, Predicate<? super AisPacket> isSecondaryTarget, Predicate<? super AisPacket> triggerSnapshot, Supplier<? extends String> snapshotDescriptionSupplier, Supplier<? extends Integer> movementInterpolationStepSupplier, BiFunction<? super ShipTypeCargo, ? super NavigationalStatus, ? extends String> iconHrefSupplier) {
//        super(filter,
//            createSituationFolder, createMovementsFolder, createTracksFolder,
//            isPrimaryTarget, isSecondaryTarget, triggerSnapshot, snapshotDescriptionSupplier, movementInterpolationStepSupplier,
//            (BiFunction<? super ShipTypeCargo, ? super NavigationalStatus, ? extends String>) (iconHrefSupplier == null ? (new BiFunction<ShipTypeCargo, NavigationalStatus, String>() {
//                @Override
//                public String apply(ShipTypeCargo shipTypeCargo, NavigationalStatus navigationalStatus) {
//                    ShipTypeColor color = ShipTypeColor.getColor(shipTypeCargo == null ? ShipTypeCargo.ShipType.UNKNOWN : shipTypeCargo.getShipType());
//                    return "icons/vessel_" + color.toString().toLowerCase() + (navigationalStatus != NavigationalStatus.MOORED ? "" : "_moored") + ".png";
//                }
//            }) : iconHrefSupplier)
//        );
//    }
//
//    /**
//     * Create a sink that writes KMZ contents to outputStream - but build the scenario only from
//     * AisPackets which comply with the filter predicate.
//     *
//     * @param filter                            a filter predicate for pre-filtering of AisPackets before they are passed to the tracker.
//     * @param createSituationFolder             create and populate the 'situation' folder in the generated KML
//     * @param createMovementsFolder             create and populate the 'movements' folder in the generated KML
//     * @param createTracksFolder                create and populate the 'tracks' folder in the generated KML
//     * @param isPrimaryTarget                   Apply primary KMZ styling to targets which are updated by packets that pass this predicate.
//     * @param isSecondaryTarget                 Apply secondary KMZ styling to targets which are updated by packets that pass this predicate.
//     * @param triggerSnapshot                   the trigger snapshot
//     * @param snapshotDescriptionSupplier       the snapshot description supplier
//     * @param movementInterpolationStepSupplier the movement interpolation step supplier
//     * @param supplyTitle                       Supplier of KMZ folder title
//     * @param supplyDescription                 Supplier of KMZ folder description
//     * @param iconHrefSupplier                  the icon href supplier
//     */
//    public AisPacketKMZOutputSink(Predicate<? super AisPacket> filter, boolean createSituationFolder, boolean createMovementsFolder, boolean createTracksFolder, Predicate<? super AisPacket> isPrimaryTarget, Predicate<? super AisPacket> isSecondaryTarget, Predicate<? super AisPacket> triggerSnapshot, Supplier<? extends String> snapshotDescriptionSupplier, Supplier<? extends Integer> movementInterpolationStepSupplier, Supplier<? extends String> supplyTitle, Supplier<? extends String> supplyDescription, BiFunction<? super ShipTypeCargo, ? super NavigationalStatus, ? extends String> iconHrefSupplier) {
//        super(filter,
//            createSituationFolder, createMovementsFolder, createTracksFolder,
//            isPrimaryTarget, isSecondaryTarget, triggerSnapshot, snapshotDescriptionSupplier, movementInterpolationStepSupplier, supplyTitle, supplyDescription,
//            (BiFunction<? super ShipTypeCargo, ? super NavigationalStatus, ? extends String>) (iconHrefSupplier == null ? (new BiFunction<ShipTypeCargo, NavigationalStatus, String>() {
//                @Override
//                public String apply(ShipTypeCargo shipTypeCargo, NavigationalStatus navigationalStatus) {
//                    ShipTypeColor color = ShipTypeColor.getColor(shipTypeCargo == null ? ShipTypeCargo.ShipType.UNKNOWN : shipTypeCargo.getShipType());
//                    return "icons/vessel_" + color.toString().toLowerCase() + (navigationalStatus != NavigationalStatus.MOORED ? "" : "_moored") + ".png";
//                }
//            }) : iconHrefSupplier)
//        );
//    }
//
//    public void footer(OutputStream outputStream, long count) throws IOException {
//        try (ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(outputStream))) { // http://goo.gl/UFb41j
//            zos.putNextEntry(new ZipEntry("doc.kml"));
//            createKml().marshal(zos);
//            zos.putNextEntry(new ZipEntry("icons/vessel_blue.png"));
//            zos.write(VESSEL_BLUE_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_blue_moored.png"));
//            zos.write(VESSEL_BLUE_MOORED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_grey.png"));
//            zos.write(VESSEL_GRAY_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_grey_moored.png"));
//            zos.write(VESSEL_GRAY_MOORED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_green.png"));
//            zos.write(VESSEL_GREEN_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_green_moored.png"));
//            zos.write(VESSEL_GREEN_MOORED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_orange.png"));
//            zos.write(VESSEL_ORANGE_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_orange_moored.png"));
//            zos.write(VESSEL_ORANGE_MOORED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_purple.png"));
//            zos.write(VESSEL_PURPLE_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_purple_moored.png"));
//            zos.write(VESSEL_PURPLE_MOORED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_red.png"));
//            zos.write(VESSEL_RED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_red_moored.png"));
//            zos.write(VESSEL_RED_MOORED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_turquoise.png"));
//            zos.write(VESSEL_TURQUOISE_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_turquoise_moored.png"));
//            zos.write(VESSEL_TURQUOISE_MOORED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_white.png"));
//            zos.write(VESSEL_WHITE_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_white_moored.png"));
//            zos.write(VESSEL_WHITE_MOORED_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_yellow.png"));
//            zos.write(VESSEL_YELLOW_PNG);
//            zos.putNextEntry(new ZipEntry("icons/vessel_yellow_moored.png"));
//            zos.write(VESSEL_YELLOW_MOORED_PNG);
//            zos.closeEntry();
//        }
//    }
//
//    /**
//     * The entry point of application.
//     *
//     * @param args the input arguments
//     * @throws IOException the io exception
//     */
//    public static void SendVoice(String[] args) throws IOException {
//        byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_blue.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_blue_moored.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_gray.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_gray_moored.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_green.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_green_moored.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_orange.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_orange_moored.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_purple.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_purple_moored.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_red.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_red_moored.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_turquoise.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_turquoise_moored.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_white.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_white_moored.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_yellow.png"));
//        //byte[] bytes = Files.toByteArray(new File("/Users/<USER>/tmp/img/vessel_yellow_moored.png"));
//
//        System.out.println("byte[] file = {");
//
//        for (int i = 0; i < bytes.length; i++) {
//
//            if (i%8 == 0) {
//                System.out.print("\t");
//            }
//
//            System.out.printf("(byte) 0x%02x", bytes[i]);
//
//            if (i != bytes.length - 1) {
//                System.out.print(", ");
//            }
//
//            if (i%8 == 7) {
//                System.out.println();
//            }
//        }
//
//        System.out.println("\n};");
//    }
//
//    private static final byte[] VESSEL_BLUE_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x1b, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0xd5, (byte) 0x31, (byte) 0x4a, (byte) 0x03,
//        (byte) 0x41, (byte) 0x18, (byte) 0x86, (byte) 0xe1, (byte) 0x57, (byte) 0x2d, (byte) 0x6c, (byte) 0xf4,
//        (byte) 0x06, (byte) 0x7a, (byte) 0x06, (byte) 0x3d, (byte) 0x84, (byte) 0x5e, (byte) 0x45, (byte) 0x2b,
//        (byte) 0x0f, (byte) 0x20, (byte) 0x82, (byte) 0x8c, (byte) 0x12, (byte) 0x25, (byte) 0x33, (byte) 0xbb,
//        (byte) 0xdb, (byte) 0x78, (byte) 0x09, (byte) 0x35, (byte) 0x5d, (byte) 0x1a, (byte) 0x9b, (byte) 0x14,
//        (byte) 0x76, (byte) 0x56, (byte) 0x49, (byte) 0xa5, (byte) 0x95, (byte) 0xa0, (byte) 0x28, (byte) 0x11,
//        (byte) 0x9b, (byte) 0x15, (byte) 0xb2, (byte) 0x0a, (byte) 0x1a, (byte) 0x12, (byte) 0x74, (byte) 0xc6,
//        (byte) 0x1f, (byte) 0x66, (byte) 0x40, (byte) 0xd0, (byte) 0x4a, (byte) 0x64, (byte) 0x67, (byte) 0x0b,
//        (byte) 0xe7, (byte) 0x83, (byte) 0x07, (byte) 0x16, (byte) 0x76, (byte) 0x61, (byte) 0x5e, (byte) 0xa6,
//        (byte) 0x59, (byte) 0xd2, (byte) 0xd2, (byte) 0xd2, (byte) 0xfe, (byte) 0xb6, (byte) 0xbd, (byte) 0x1d,
//        (byte) 0xb1, (byte) 0xdd, (byte) 0x64, (byte) 0x80, (byte) 0xf2, (byte) 0x98, (byte) 0xf1, (byte) 0xa2,
//        (byte) 0xaf, (byte) 0x75, (byte) 0xe7, (byte) 0x35, (byte) 0x12, (byte) 0xa0, (byte) 0xd6, (byte) 0x40,
//        (byte) 0x8f, (byte) 0xc1, (byte) 0x88, (byte) 0xdd, (byte) 0x75, (byte) 0x70, (byte) 0x31, (byte) 0x03,
//        (byte) 0xd4, (byte) 0x2c, (byte) 0xe8, (byte) 0x1b, (byte) 0xe8, (byte) 0x5a, (byte) 0xe1, (byte) 0x40,
//        (byte) 0xdf, (byte) 0x42, (byte) 0x67, (byte) 0x2e, (byte) 0xe2, (byte) 0x2d, (byte) 0x1c, (byte) 0x6c,
//        (byte) 0xc0, (byte) 0xf1, (byte) 0x33, (byte) 0x9c, (byte) 0x5b, (byte) 0xe1, (byte) 0xe0, (byte) 0xe4,
//        (byte) 0x05, (byte) 0x0e, (byte) 0x37, (byte) 0x23, (byte) 0xdd, (byte) 0x82, (byte) 0x5a, (byte) 0x00,
//        (byte) 0x5d, (byte) 0x42, (byte) 0x15, (byte) 0x0e, (byte) 0x17, (byte) 0x54, (byte) 0xa2, (byte) 0xfd,
//        (byte) 0x04, (byte) 0x7a, (byte) 0x31, (byte) 0xc2, (byte) 0x2d, (byte) 0x98, (byte) 0x0c, (byte) 0x7a,
//        (byte) 0x6f, (byte) 0x60, (byte) 0xdd, (byte) 0x57, (byte) 0x80, (byte) 0x13, (byte) 0xbd, (byte) 0x31,
//        (byte) 0x98, (byte) 0xbc, (byte) 0xe6, (byte) 0x5b, (byte) 0x50, (byte) 0x4b, (byte) 0x60, (byte) 0x46,
//        (byte) 0x30, (byte) 0xb1, (byte) 0xe0, (byte) 0xbe, (byte) 0x05, (byte) 0x4c, (byte) 0x84, (byte) 0xa9,
//        (byte) 0xa0, (byte) 0xb5, (byte) 0x4c, (byte) 0x7d, (byte) 0xcb, (byte) 0x4e, (byte) 0xa1, (byte) 0xff,
//        (byte) 0x0e, (byte) 0x36, (byte) 0x1c, (byte) 0xda, (byte) 0x0d, (byte) 0x5c, (byte) 0x30, (byte) 0xf8,
//        (byte) 0x80, (byte) 0xbc, (byte) 0x43, (byte) 0x7d, (byte) 0xcb, (byte) 0x2f, (byte) 0xa1, (byte) 0xb4,
//        (byte) 0x3e, (byte) 0x60, (byte) 0x28, (byte) 0x54, (byte) 0xf0, (byte) 0x10, (byte) 0x02, (byte) 0x4a,
//        (byte) 0x51, (byte) 0x5c, (byte) 0xfd, (byte) 0x97, (byte) 0x00, (byte) 0x27, (byte) 0x86, (byte) 0x81,
//        (byte) 0x6b, (byte) 0x26, (byte) 0x20, (byte) 0x48, (byte) 0x01, (byte) 0x4d, (byte) 0x06, (byte) 0x3c,
//        (byte) 0x7a, (byte) 0xf1, (byte) 0x03, (byte) 0xa6, (byte) 0x0e, (byte) 0xce, (byte) 0x5e, (byte) 0x41,
//        (byte) 0xdf, (byte) 0x43, (byte) 0x5b, (byte) 0xc8, (byte) 0x33, (byte) 0xd3, (byte) 0x58, (byte) 0x01,
//        (byte) 0x03, (byte) 0x0b, (byte) 0x45, (byte) 0x05, (byte) 0x26, (byte) 0x87, (byte) 0xa3, (byte) 0x79,
//        (byte) 0xd8, (byte) 0x12, (byte) 0x26, (byte) 0x83, (byte) 0x62, (byte) 0x04, (byte) 0x7d, (byte) 0x07,
//        (byte) 0x79, (byte) 0x9d, (byte) 0x01, (byte) 0xd9, (byte) 0x05, (byte) 0xe8, (byte) 0x6b, (byte) 0x50,
//        (byte) 0xab, (byte) 0x3f, (byte) 0x7f, (byte) 0x3a, (byte) 0xfb, (byte) 0x2b, (byte) 0xfe, (byte) 0x9d,
//        (byte) 0x7c, (byte) 0x93, (byte) 0x96, (byte) 0x96, (byte) 0xf6, (byte) 0x8b, (byte) 0x7d, (byte) 0x02,
//        (byte) 0x62, (byte) 0x24, (byte) 0xfa, (byte) 0x55, (byte) 0x5e, (byte) 0xc3, (byte) 0x95, (byte) 0xd4,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44,
//        (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_BLUE_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x63, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x94, (byte) 0xb1, (byte) 0x0d, (byte) 0x80,
//        (byte) 0x30, (byte) 0x0c, (byte) 0x04, (byte) 0x3d, (byte) 0x03, (byte) 0x13, (byte) 0xa7, (byte) 0x65,
//        (byte) 0x38, (byte) 0x16, (byte) 0xa0, (byte) 0x35, (byte) 0x5b, (byte) 0x50, (byte) 0x04, (byte) 0x5e,
//        (byte) 0x82, (byte) 0xca, (byte) 0x5d, (byte) 0x0a, (byte) 0x7f, (byte) 0x73, (byte) 0x27, (byte) 0x9d,
//        (byte) 0x94, (byte) 0xee, (byte) 0x5f, (byte) 0x72, (byte) 0xec, (byte) 0x00, (byte) 0x00, (byte) 0x80,
//        (byte) 0x75, (byte) 0xc6, (byte) 0x16, (byte) 0xb1, (byte) 0x9f, (byte) 0x52, (byte) 0x6f, (byte) 0x53,
//        (byte) 0xf8, (byte) 0x71, (byte) 0xcb, (byte) 0xbf, (byte) 0x84, (byte) 0x21, (byte) 0x7c, (byte) 0x4e,
//        (byte) 0x59, (byte) 0x4a, (byte) 0xf4, (byte) 0x86, (byte) 0xd7, (byte) 0x12, (byte) 0x8d, (byte) 0xe1,
//        (byte) 0x97, (byte) 0xec, (byte) 0x29, (byte) 0x51, (byte) 0xc3, (byte) 0xf3, (byte) 0x75, (byte) 0x7c,
//        (byte) 0x66, (byte) 0x29, (byte) 0x61, (byte) 0x2f, (byte) 0xd0, (byte) 0x34, (byte) 0x82, (byte) 0x94,
//        (byte) 0xee, (byte) 0x7f, (byte) 0x50, (byte) 0xc3, (byte) 0xad, (byte) 0x6b, (byte) 0x68, (byte) 0x3d,
//        (byte) 0x44, (byte) 0xde, (byte) 0x53, (byte) 0x0c, (byte) 0x00, (byte) 0x00, (byte) 0x8b, (byte) 0x3c,
//        (byte) 0xbe, (byte) 0x52, (byte) 0x9d, (byte) 0x27, (byte) 0x56, (byte) 0x59, (byte) 0x40, (byte) 0x84,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44,
//        (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_GRAY_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x04, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0xd9, (byte) 0x73, (byte) 0xb2,
//        (byte) 0x7f, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x23, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0x01, (byte) 0x63, (byte) 0x18, (byte) 0x04, (byte) 0x60, (byte) 0x14,
//        (byte) 0x30, (byte) 0xe2, (byte) 0x97, (byte) 0x36, (byte) 0xa9, (byte) 0xfe, (byte) 0xff, (byte) 0x8f,
//        (byte) 0xa1, (byte) 0x1d, (byte) 0xaf, (byte) 0x01, (byte) 0xc6, (byte) 0xf8, (byte) 0xcd, (byte) 0x6f,
//        (byte) 0x60, (byte) 0x60, (byte) 0x38, (byte) 0xd3, (byte) 0x08, (byte) 0x64, (byte) 0xfc, (byte) 0xc7,
//        (byte) 0xa5, (byte) 0x82, (byte) 0xe5, (byte) 0x2c, (byte) 0x5e, (byte) 0x03, (byte) 0x2c, (byte) 0x12,
//        (byte) 0x80, (byte) 0x44, (byte) 0x23, (byte) 0x3e, (byte) 0x15, (byte) 0x4c, (byte) 0xf8, (byte) 0x24,
//        (byte) 0x4d, (byte) 0x5d, (byte) 0x38, (byte) 0xc4, (byte) 0x39, (byte) 0xc4, (byte) 0x4d, (byte) 0x5c,
//        (byte) 0xc8, (byte) 0x0d, (byte) 0x1f, (byte) 0x26, (byte) 0xdb, (byte) 0xbb, (byte) 0x79, (byte) 0xff,
//        (byte) 0xf2, (byte) 0xfe, (byte) 0xdb, (byte) 0xde, (byte) 0x63, (byte) 0x60, (byte) 0x66, (byte) 0x60,
//        (byte) 0x24, (byte) 0xc3, (byte) 0x05, (byte) 0xe6, (byte) 0x29, (byte) 0xaa, (byte) 0x22, (byte) 0x42,
//        (byte) 0x0c, (byte) 0x82, (byte) 0x0c, (byte) 0xaa, (byte) 0x22, (byte) 0x66, (byte) 0x29, (byte) 0x64,
//        (byte) 0x78, (byte) 0x41, (byte) 0x9b, (byte) 0x87, (byte) 0xad, (byte) 0x35, (byte) 0x82, (byte) 0xf7,
//        (byte) 0x3f, (byte) 0xd0, (byte) 0xe6, (byte) 0x08, (byte) 0x5e, (byte) 0xf6, (byte) 0x56, (byte) 0x6b,
//        (byte) 0x1e, (byte) 0x06, (byte) 0x46, (byte) 0x12, (byte) 0x0d, (byte) 0x10, (byte) 0xaa, (byte) 0xb7,
//        (byte) 0xe5, (byte) 0x16, (byte) 0x64, (byte) 0x04, (byte) 0xb3, (byte) 0x18, (byte) 0x6c, (byte) 0xb9,
//        (byte) 0x99, (byte) 0xeb, (byte) 0x71, (byte) 0xc6, (byte) 0x02, (byte) 0x0e, (byte) 0xe7, (byte) 0xcb,
//        (byte) 0x30, (byte) 0xa7, (byte) 0x78, (byte) 0x73, (byte) 0xc0, (byte) 0x78, (byte) 0xde, (byte) 0x1c,
//        (byte) 0xc7, (byte) 0x53, (byte) 0xec, (byte) 0x26, (byte) 0x32, (byte) 0x3c, (byte) 0xc6, (byte) 0x6a,
//        (byte) 0x80, (byte) 0x1d, (byte) 0x56, (byte) 0x03, (byte) 0x98, (byte) 0x7b, (byte) 0xfd, (byte) 0x79,
//        (byte) 0xd9, (byte) 0x18, (byte) 0x19, (byte) 0x19, (byte) 0xde, (byte) 0x83, (byte) 0x79, (byte) 0xec,
//        (byte) 0x0c, (byte) 0xfe, (byte) 0xbc, (byte) 0x9b, (byte) 0x7a, (byte) 0x19, (byte) 0xc2, (byte) 0xb0,
//        (byte) 0x1a, (byte) 0x80, (byte) 0xdd, (byte) 0x09, (byte) 0xff, (byte) 0xb5, (byte) 0x54, (byte) 0x80,
//        (byte) 0x9e, (byte) 0xbb, (byte) 0xc7, (byte) 0x70, (byte) 0x0c, (byte) 0x98, (byte) 0x82, (byte) 0x6c,
//        (byte) 0x19, (byte) 0x14, (byte) 0x18, (byte) 0x80, (byte) 0x3c, (byte) 0x2d, (byte) 0x5c, (byte) 0x61,
//        (byte) 0x40, (byte) 0x21, (byte) 0x60, (byte) 0xc1, (byte) 0x27, (byte) 0xa9, (byte) 0xc4, (byte) 0x50,
//        (byte) 0x06, (byte) 0x24, (byte) 0x15, (byte) 0xc9, (byte) 0x31, (byte) 0x00, (byte) 0x61, (byte) 0x04,
//        (byte) 0x02, (byte) 0x90, (byte) 0xe7, (byte) 0x85, (byte) 0x01, (byte) 0x37, (byte) 0xe0, (byte) 0x21,
//        (byte) 0x10, (byte) 0x92, (byte) 0x6d, (byte) 0xc0, (byte) 0x6f, (byte) 0x86, (byte) 0x95, (byte) 0xdf,
//        (byte) 0x26, (byte) 0x3e, (byte) 0x9a, (byte) 0xf0, (byte) 0x68, (byte) 0xe5, (byte) 0xb7, (byte) 0x5f,
//        (byte) 0xe4, (byte) 0x04, (byte) 0xe2, (byte) 0x1d, (byte) 0x86, (byte) 0x2d, (byte) 0x1f, (byte) 0x7f,
//        (byte) 0xcc, (byte) 0x79, (byte) 0x51, (byte) 0xcd, (byte) 0xc0, (byte) 0x70, (byte) 0xa2, (byte) 0xe5,
//        (byte) 0x7c, (byte) 0x8a, (byte) 0x8f, (byte) 0x00, (byte) 0xae, (byte) 0x12, (byte) 0x85, (byte) 0xd1,
//        (byte) 0x11, (byte) 0xab, (byte) 0xf0, (byte) 0xbf, (byte) 0x63, (byte) 0xff, (byte) 0x44, (byte) 0x3e,
//        (byte) 0x87, (byte) 0x5d, (byte) 0xb8, (byte) 0x08, (byte) 0x29, (byte) 0x89, (byte) 0x0c, (byte) 0xf5,
//        (byte) 0x79, (byte) 0x56, (byte) 0x33, (byte) 0xbd, (byte) 0x61, (byte) 0xb0, (byte) 0xc2, (byte) 0x6a,
//        (byte) 0x80, (byte) 0xfd, (byte) 0xc0, (byte) 0xc7, (byte) 0xc2, (byte) 0xa8, (byte) 0x01, (byte) 0x00,
//        (byte) 0xd3, (byte) 0x8c, (byte) 0x40, (byte) 0x08, (byte) 0xc1, (byte) 0xe5, (byte) 0x7a, (byte) 0x71,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44,
//        (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_GRAY_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x71, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x94, (byte) 0xb1, (byte) 0x09, (byte) 0xc0,
//        (byte) 0x30, (byte) 0x0c, (byte) 0x04, (byte) 0xb3, (byte) 0x88, (byte) 0xab, (byte) 0x8c, (byte) 0x20,
//        (byte) 0xbc, (byte) 0x42, (byte) 0x56, (byte) 0xc8, (byte) 0x54, (byte) 0x86, (byte) 0xcc, (byte) 0x21,
//        (byte) 0xad, (byte) 0xa2, (byte) 0x89, (byte) 0xd2, (byte) 0x38, (byte) 0x7c, (byte) 0x6b, (byte) 0xa5,
//        (byte) 0x72, (byte) 0x21, (byte) 0x35, (byte) 0x7f, (byte) 0xf0, (byte) 0xa0, (byte) 0xee, (byte) 0x1e,
//        (byte) 0x6c, (byte) 0xe9, (byte) 0x20, (byte) 0x84, (byte) 0x10, (byte) 0xb2, (byte) 0x4b, (byte) 0x6b,
//        (byte) 0xf7, (byte) 0xd9, (byte) 0xfb, (byte) 0xf5, (byte) 0x22, (byte) 0x98, (byte) 0x4b, (byte) 0xe4,
//        (byte) 0x63, (byte) 0x3c, (byte) 0x13, (byte) 0x09, (byte) 0x25, (byte) 0xb2, (byte) 0xe4, (byte) 0xee,
//        (byte) 0x8e, (byte) 0x84, (byte) 0x12, (byte) 0x99, (byte) 0xf2, (byte) 0xdf, (byte) 0x12, (byte) 0x69,
//        (byte) 0x72, (byte) 0x33, (byte) 0x43, (byte) 0x72, (byte) 0x4a, (byte) 0xac, (byte) 0x72, (byte) 0x55,
//        (byte) 0x9d, (byte) 0x22, (byte) 0x82, (byte) 0x60, (byte) 0x0e, (byte) 0x25, (byte) 0xca, (byte) 0x0b,
//        (byte) 0x64, (byte) 0x3c, (byte) 0x01, (byte) 0xc4, (byte) 0x48, (byte) 0xe9, (byte) 0x3f, (byte) 0xa8,
//        (byte) 0xde, (byte) 0x84, (byte) 0x28, (byte) 0x2f, (byte) 0x3d, (byte) 0x44, (byte) 0xb5, (byte) 0xa7,
//        (byte) 0x98, (byte) 0x10, (byte) 0x42, (byte) 0xc8, (byte) 0x26, (byte) 0x1f, (byte) 0x79, (byte) 0xb7,
//        (byte) 0xe8, (byte) 0x57, (byte) 0x7a, (byte) 0x42, (byte) 0xa2, (byte) 0x32, (byte) 0x00, (byte) 0x00,
//        (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42,
//        (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_GREEN_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x21, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x95, (byte) 0x31, (byte) 0x4a, (byte) 0x03,
//        (byte) 0x41, (byte) 0x14, (byte) 0x86, (byte) 0x3f, (byte) 0xb5, (byte) 0xb0, (byte) 0xd1, (byte) 0x1b,
//        (byte) 0xe8, (byte) 0x19, (byte) 0xf4, (byte) 0x10, (byte) 0x7a, (byte) 0x15, (byte) 0xad, (byte) 0x3c,
//        (byte) 0x80, (byte) 0x08, (byte) 0x32, (byte) 0x4a, (byte) 0x94, (byte) 0xcc, (byte) 0xec, (byte) 0xda,
//        (byte) 0xe4, (byte) 0x12, (byte) 0x6a, (byte) 0xba, (byte) 0x34, (byte) 0x36, (byte) 0x29, (byte) 0xec,
//        (byte) 0xac, (byte) 0x92, (byte) 0x4a, (byte) 0x2b, (byte) 0x41, (byte) 0x51, (byte) 0x22, (byte) 0x36,
//        (byte) 0x2b, (byte) 0x64, (byte) 0x15, (byte) 0x54, (byte) 0x12, (byte) 0x74, (byte) 0xc6, (byte) 0x37,
//        (byte) 0xc3, (byte) 0x54, (byte) 0x5a, (byte) 0x89, (byte) 0xec, (byte) 0x6e, (byte) 0xe1, (byte) 0xfc,
//        (byte) 0x8f, (byte) 0x0f, (byte) 0x96, (byte) 0xd9, (byte) 0x85, (byte) 0xff, (byte) 0xe3, (byte) 0xb1,
//        (byte) 0x30, (byte) 0xa4, (byte) 0xa4, (byte) 0xa4, (byte) 0xfc, (byte) 0x29, (byte) 0x7b, (byte) 0xec,
//        (byte) 0x08, (byte) 0xdb, (byte) 0x4d, (byte) 0x0a, (byte) 0xa8, (byte) 0x00, (byte) 0xcc, (byte) 0x04,
//        (byte) 0x6a, (byte) 0x4f, (byte) 0x8b, (byte) 0x3b, (byte) 0x4f, (byte) 0x33, (byte) 0x02, (byte) 0x8a,
//        (byte) 0x35, (byte) 0x34, (byte) 0xef, (byte) 0x18, (byte) 0x61, (byte) 0x97, (byte) 0x75, (byte) 0x5c,
//        (byte) 0x9d, (byte) 0x02, (byte) 0x8a, (byte) 0x59, (byte) 0x34, (byte) 0x37, (byte) 0xf4, (byte) 0xb0,
//        (byte) 0x82, (byte) 0x43, (byte) 0x73, (byte) 0x4b, (byte) 0x97, (byte) 0xb9, (byte) 0xfa, (byte) 0xb6,
//        (byte) 0x70, (byte) 0xc0, (byte) 0x06, (byte) 0xc7, (byte) 0x3c, (byte) 0x73, (byte) 0x8e, (byte) 0x15,
//        (byte) 0x1c, (byte) 0x27, (byte) 0xbc, (byte) 0x70, (byte) 0xc8, (byte) 0x66, (byte) 0x3d, (byte) 0x5b,
//        (byte) 0x50, (byte) 0x2c, (byte) 0xa0, (byte) 0x29, (byte) 0x28, (byte) 0x63, (byte) 0xb9, (byte) 0xa7,
//        (byte) 0x14, (byte) 0xda, (byte) 0x3c, (byte) 0xa1, (byte) 0x59, (byte) 0xac, (byte) 0x7e, (byte) 0x0b,
//        (byte) 0x86, (byte) 0x8c, (byte) 0x3e, (byte) 0x6f, (byte) 0xd8, (byte) 0x58, (byte) 0x2e, (byte) 0x84,
//        (byte) 0xe9, (byte) 0x87, (byte) 0xff, (byte) 0x21, (byte) 0xaf, (byte) 0x76, (byte) 0x0b, (byte) 0x8a,
//        (byte) 0x25, (byte) 0x0c, (byte) 0x63, (byte) 0x26, (byte) 0xbe, (byte) 0xfe, (byte) 0x9b, (byte) 0xc0,
//        (byte) 0x44, (byte) 0x30, (byte) 0x94, (byte) 0xb4, (byte) 0x58, (byte) 0xa6, (byte) 0xb2, (byte) 0x64,
//        (byte) 0x9c, (byte) 0x32, (byte) 0xe0, (byte) 0x03, (byte) 0x1b, (byte) 0x4b, (byte) 0x7b, (byte) 0x91,
//        (byte) 0x38, (byte) 0x0c, (byte) 0xf9, (byte) 0x24, (byte) 0xa7, (byte) 0x4b, (byte) 0x65, (byte) 0xc9,
//        (byte) 0xb9, (byte) 0xa4, (byte) 0xc0, (byte) 0x06, (byte) 0x81, (byte) 0x91, (byte) 0xa0, (byte) 0x22,
//        (byte) 0x0f, (byte) 0x51, (byte) 0xa0, (byte) 0x10, (byte) 0x8e, (byte) 0xb8, (byte) 0xfa, (byte) 0x1f,
//        (byte) 0x02, (byte) 0x61, (byte) 0x46, (byte) 0x11, (byte) 0xd7, (byte) 0x8c, (byte) 0x40, (byte) 0x9c,
//        (byte) 0x24, (byte) 0xd0, (byte) 0xa4, (byte) 0xc0, (byte) 0xa3, (byte) 0xa7, (byte) 0x09, (byte) 0x81,
//        (byte) 0x29, (byte) 0x8e, (byte) 0x33, (byte) 0x5e, (byte) 0xd1, (byte) 0xdc, (byte) 0xd3, (byte) 0x16,
//        (byte) 0xfc, (byte) 0xf3, (byte) 0xb4, (byte) 0x2e, (byte) 0x81, (byte) 0x21, (byte) 0x56, (byte) 0x4a,
//        (byte) 0x4a, (byte) 0x0c, (byte) 0x39, (byte) 0x1d, (byte) 0xe6, (byte) 0xd9, (byte) 0x12, (byte) 0x0c,
//        (byte) 0x99, (byte) 0x9c, (byte) 0x8d, (byte) 0x19, (byte) 0xe0, (byte) 0xc8, (byte) 0xab, (byte) 0x14,
//        (byte) 0xc8, (byte) 0xb8, (byte) 0x40, (byte) 0x73, (byte) 0x8d, (byte) 0x62, (byte) 0xf5, (byte) 0xc7,
//        (byte) 0xa5, (byte) 0xb3, (byte) 0xcf, (byte) 0x8a, (byte) 0x7f, (byte) 0x17, (byte) 0xbe, (byte) 0x49,
//        (byte) 0x49, (byte) 0x49, (byte) 0xf9, (byte) 0x45, (byte) 0xbe, (byte) 0x00, (byte) 0xd8, (byte) 0xb0,
//        (byte) 0xfa, (byte) 0x55, (byte) 0x02, (byte) 0x78, (byte) 0x5d, (byte) 0x75, (byte) 0x00, (byte) 0x00,
//        (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42,
//        (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_GREEN_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x6b, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x58, (byte) 0xc3, (byte) 0x63, (byte) 0x60, (byte) 0x18, (byte) 0x05, (byte) 0xa3,
//        (byte) 0x60, (byte) 0x14, (byte) 0x8c, (byte) 0x82, (byte) 0x51, (byte) 0x40, (byte) 0x26, (byte) 0x10,
//        (byte) 0x71, (byte) 0xb4, (byte) 0x31, (byte) 0x66, (byte) 0xaf, (byte) 0xe5, (byte) 0xfc, (byte) 0x07,
//        (byte) 0xc2, (byte) 0x20, (byte) 0xf6, (byte) 0x80, (byte) 0x58, (byte) 0xce, (byte) 0x74, (byte) 0x9a,
//        (byte) 0xe9, (byte) 0x3f, (byte) 0x08, (byte) 0xd3, (byte) 0xd5, (byte) 0x11, (byte) 0xc8, (byte) 0x96,
//        (byte) 0x33, (byte) 0x40, (byte) 0x21, (byte) 0xdd, (byte) 0x1c, (byte) 0x81, (byte) 0xcd, (byte) 0x72,
//        (byte) 0xba, (byte) 0x39, (byte) 0x02, (byte) 0xab, (byte) 0xe5, (byte) 0x4f, (byte) 0xa0, (byte) 0x98,
//        (byte) 0x1e, (byte) 0x8e, (byte) 0xc0, (byte) 0xb0, (byte) 0xfc, (byte) 0x31, (byte) 0x10, (byte) 0x37,
//        (byte) 0x40, (byte) 0xf1, (byte) 0x63, (byte) 0x4c, (byte) 0x47, (byte) 0x0c, (byte) 0x3f, (byte) 0x07,
//        (byte) 0x60, (byte) 0x8d, (byte) 0x82, (byte) 0xc7, (byte) 0xd8, (byte) 0x2d, (byte) 0xa7, (byte) 0x6f,
//        (byte) 0x3a, (byte) 0x18, (byte) 0x0c, (byte) 0x39, (byte) 0x61, (byte) 0x40, (byte) 0xcb, (byte) 0x02,
//        (byte) 0xba, (byte) 0x5b, (byte) 0x3e, (byte) 0x28, (byte) 0x8a, (byte) 0xe2, (byte) 0x51, (byte) 0x30,
//        (byte) 0x0a, (byte) 0x46, (byte) 0xc1, (byte) 0x28, (byte) 0x18, (byte) 0x56, (byte) 0x00, (byte) 0x00,
//        (byte) 0x84, (byte) 0x42, (byte) 0xa5, (byte) 0x47, (byte) 0xaa, (byte) 0x71, (byte) 0x67, (byte) 0x53,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44,
//        (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_ORANGE_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x52, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x94, (byte) 0xbd, (byte) 0x4a, (byte) 0x03,
//        (byte) 0x41, (byte) 0x14, (byte) 0x85, (byte) 0x8f, (byte) 0x5a, (byte) 0xd8, (byte) 0xe8, (byte) 0x1b,
//        (byte) 0xe8, (byte) 0x33, (byte) 0x68, (byte) 0x2f, (byte) 0x48, (byte) 0x20, (byte) 0xfb, (byte) 0x83,
//        (byte) 0x95, (byte) 0x44, (byte) 0x92, (byte) 0xc2, (byte) 0x6a, (byte) 0x51, (byte) 0x74, (byte) 0x7d,
//        (byte) 0x06, (byte) 0xb1, (byte) 0xd9, (byte) 0x5e, (byte) 0x2b, (byte) 0xdf, (byte) 0x41, (byte) 0x31,
//        (byte) 0x1b, (byte) 0x97, (byte) 0xdd, (byte) 0x35, (byte) 0xe2, (byte) 0x1b, (byte) 0x98, (byte) 0x26,
//        (byte) 0x20, (byte) 0xa8, (byte) 0xa9, (byte) 0x84, (byte) 0x04, (byte) 0xad, (byte) 0x8d, (byte) 0x7f,
//        (byte) 0x81, (byte) 0x98, (byte) 0xc2, (byte) 0xb8, (byte) 0xe3, (byte) 0x19, (byte) 0xa6, (byte) 0x4b,
//        (byte) 0x27, (byte) 0xb2, (byte) 0xb3, (byte) 0x85, (byte) 0x73, (byte) 0xe0, (byte) 0x83, (byte) 0xcb,
//        (byte) 0xdc, (byte) 0x85, (byte) 0xfb, (byte) 0xcd, (byte) 0xb0, (byte) 0x5c, (byte) 0x98, (byte) 0x98,
//        (byte) 0x98, (byte) 0xfc, (byte) 0x25, (byte) 0xa1, (byte) 0x85, (byte) 0x83, (byte) 0x46, (byte) 0x19,
//        (byte) 0xfb, (byte) 0x28, (byte) 0x2a, (byte) 0x0d, (byte) 0x07, (byte) 0x81, (byte) 0x84, (byte) 0xe5,
//        (byte) 0x94, (byte) 0x42, (byte) 0x73, (byte) 0xce, (byte) 0x6d, (byte) 0x3c, (byte) 0x4a, (byte) 0x04,
//        (byte) 0x87, (byte) 0x0b, (byte) 0xdd, (byte) 0x02, (byte) 0x75, (byte) 0x0b, (byte) 0x56, (byte) 0xea,
//        (byte) 0x62, (byte) 0x74, (byte) 0x41, (byte) 0x4e, (byte) 0xcb, (byte) 0xb0, (byte) 0xb5, (byte) 0x0a,
//        (byte) 0x04, (byte) 0x01, (byte) 0xa6, (byte) 0x63, (byte) 0x07, (byte) 0xdd, (byte) 0x76, (byte) 0x05,
//        (byte) 0x19, (byte) 0x11, (byte) 0x89, (byte) 0x83, (byte) 0x5e, (byte) 0x58, (byte) 0xc3, (byte) 0x8c,
//        (byte) 0x36, (byte) 0x89, (byte) 0xc8, (byte) 0x81, (byte) 0x7f, (byte) 0xbd, (byte) 0x8e, (byte) 0x8f,
//        (byte) 0x4e, (byte) 0x0d, (byte) 0x19, (byte) 0x11, (byte) 0xac, (byte) 0x07, (byte) 0x91, (byte) 0x8d,
//        (byte) 0x3d, (byte) 0x2d, (byte) 0x02, (byte) 0x61, (byte) 0x09, (byte) 0x73, (byte) 0xb1, (byte) 0x8b,
//        (byte) 0xe7, (byte) 0xe1, (byte) 0xb6, (byte) 0x1a, (byte) 0x2e, (byte) 0x61, (byte) 0x2d, (byte) 0xf8,
//        (byte) 0x22, (byte) 0xfd, (byte) 0x74, (byte) 0x05, (byte) 0xf3, (byte) 0xb9, (byte) 0x4b, (byte) 0xf0,
//        (byte) 0xb9, (byte) 0x0f, (byte) 0x6f, (byte) 0xab, (byte) 0xf8, (byte) 0xcc, (byte) 0x7c, (byte) 0x39,
//        (byte) 0x5c, (byte) 0x21, (byte) 0x58, (byte) 0xdf, (byte) 0x55, (byte) 0x31, (byte) 0x4a, (byte) 0x6d,
//        (byte) 0x1c, (byte) 0xe5, (byte) 0x2a, (byte) 0x10, (byte) 0x95, (byte) 0xb0, (byte) 0x90, (byte) 0xba,
//        (byte) 0x78, (byte) 0xfb, (byte) 0xda, (byte) 0x41, (byte) 0x26, (byte) 0x26, (byte) 0x04, (byte) 0x78,
//        (byte) 0x26, (byte) 0xd8, (byte) 0x7b, (byte) 0x4f, (byte) 0x56, (byte) 0xb1, (byte) 0x88, (byte) 0xbc,
//        (byte) 0xc2, (byte) 0x3f, (byte) 0xbe, (byte) 0xde, (byte) 0xdb, (byte) 0xc4, (byte) 0x38, (byte) 0xf3,
//        (byte) 0xd5, (byte) 0xd0, (byte) 0x76, (byte) 0x45, (byte) 0x22, (byte) 0x6b, (byte) 0x05, (byte) 0x7b,
//        (byte) 0xdf, (byte) 0x4d, (byte) 0x17, (byte) 0x61, (byte) 0x9e, (byte) 0x02, (byte) 0xf7, (byte) 0x83,
//        (byte) 0x2d, (byte) 0x75, (byte) 0xfb, (byte) 0xbe, (byte) 0x07, (byte) 0x11, (byte) 0x5a, (byte) 0x8a,
//        (byte) 0x17, (byte) 0x4f, (byte) 0x09, (byte) 0xb0, (byte) 0x27, (byte) 0xf8, (byte) 0x4d, (byte) 0xe7,
//        (byte) 0x7f, (byte) 0x08, (byte) 0x28, (byte) 0x09, (byte) 0x05, (byte) 0xeb, (byte) 0x62, (byte) 0x04,
//        (byte) 0x14, (byte) 0x46, (byte) 0xa0, (byte) 0x40, (byte) 0x81, (byte) 0x57, (byte) 0x4f, (byte) 0xa1,
//        (byte) 0x5d, (byte) 0x60, (byte) 0xbc, (byte) 0x0b, (byte) 0x71, (byte) 0xb3, (byte) 0x81, (byte) 0x61,
//        (byte) 0xe2, (byte) 0xe0, (byte) 0x49, (byte) 0x22, (byte) 0x6b, (byte) 0x9e, (byte) 0xe9, (byte) 0x11,
//        (byte) 0xe0, (byte) 0xb2, (byte) 0xc9, (byte) 0x2e, (byte) 0xb9, (byte) 0xf1, (byte) 0x62, (byte) 0xae,
//        (byte) 0xdd, (byte) 0xab, (byte) 0x35, (byte) 0xcc, (byte) 0x1e, (byte) 0x13, (byte) 0xb9, (byte) 0x9e,
//        (byte) 0x9b, (byte) 0xdc, (byte) 0x90, (byte) 0xec, (byte) 0xe5, (byte) 0x2b, (byte) 0x90, (byte) 0xb8,
//        (byte) 0x68, (byte) 0xc5, (byte) 0x0e, (byte) 0x1e, (byte) 0x4e, (byte) 0x6c, (byte) 0x2c, (byte) 0x4f,
//        (byte) 0xee, (byte) 0xfc, (byte) 0x33, (byte) 0x17, (byte) 0x4b, (byte) 0xb2, (byte) 0x47, (byte) 0x81,
//        (byte) 0x16, (byte) 0x4c, (byte) 0x4c, (byte) 0x4c, (byte) 0x7e, (byte) 0x91, (byte) 0x1f, (byte) 0x51,
//        (byte) 0x6e, (byte) 0x3f, (byte) 0x02, (byte) 0xa0, (byte) 0xd9, (byte) 0x51, (byte) 0xba, (byte) 0x00,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae,
//        (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_ORANGE_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x6f, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x58, (byte) 0xc3, (byte) 0x63, (byte) 0x60, (byte) 0x18, (byte) 0x05, (byte) 0xa3,
//        (byte) 0x60, (byte) 0x14, (byte) 0x8c, (byte) 0x82, (byte) 0x51, (byte) 0x40, (byte) 0x26, (byte) 0xb0,
//        (byte) 0x76, (byte) 0x56, (byte) 0x77, (byte) 0x9c, (byte) 0xe6, (byte) 0xcb, (byte) 0xf8, (byte) 0x1f,
//        (byte) 0x84, (byte) 0x41, (byte) 0xec, (byte) 0x01, (byte) 0xb1, (byte) 0xfc, (byte) 0x66, (byte) 0x24,
//        (byte) 0x03, (byte) 0x18, (byte) 0xd3, (byte) 0xd5, (byte) 0x11, (byte) 0xc8, (byte) 0x96, (byte) 0xff,
//        (byte) 0x4f, (byte) 0x83, (byte) 0x60, (byte) 0xba, (byte) 0x39, (byte) 0x02, (byte) 0x9b, (byte) 0xe5,
//        (byte) 0x74, (byte) 0x73, (byte) 0x04, (byte) 0x36, (byte) 0xcb, (byte) 0xdf, (byte) 0xc6, (byte) 0x43,
//        (byte) 0x30, (byte) 0x5d, (byte) 0x1c, (byte) 0x81, (byte) 0x6e, (byte) 0xf9, (byte) 0x1b, (byte) 0xa0,
//        (byte) 0xc5, (byte) 0xab, (byte) 0x5c, (byte) 0x20, (byte) 0xf8, (byte) 0x0d, (byte) 0x16, (byte) 0x47,
//        (byte) 0x0c, (byte) 0x3f, (byte) 0x07, (byte) 0x60, (byte) 0x8b, (byte) 0x02, (byte) 0x90, (byte) 0xc5,
//        (byte) 0x6f, (byte) 0xe2, (byte) 0x07, (byte) 0x38, (byte) 0x1d, (byte) 0x0c, (byte) 0x8a, (byte) 0x9c,
//        (byte) 0x30, (byte) 0xa0, (byte) 0x65, (byte) 0x01, (byte) 0xdd, (byte) 0x2d, (byte) 0x1f, (byte) 0x14,
//        (byte) 0x45, (byte) 0xf1, (byte) 0x28, (byte) 0x18, (byte) 0x05, (byte) 0xa3, (byte) 0x60, (byte) 0x14,
//        (byte) 0x0c, (byte) 0x2b, (byte) 0x00, (byte) 0x00, (byte) 0x39, (byte) 0xb5, (byte) 0xc8, (byte) 0x0b,
//        (byte) 0xca, (byte) 0x2d, (byte) 0x82, (byte) 0x82, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00,
//        (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_PURPLE_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x52, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x55, (byte) 0x31, (byte) 0x4e, (byte) 0x02,
//        (byte) 0x51, (byte) 0x10, (byte) 0x7d, (byte) 0x68, (byte) 0x41, (byte) 0xa3, (byte) 0x37, (byte) 0xd0,
//        (byte) 0x33, (byte) 0xe8, (byte) 0x21, (byte) 0xf4, (byte) 0x2a, (byte) 0x5a, (byte) 0x71, (byte) 0x00,
//        (byte) 0x63, (byte) 0x42, (byte) 0x16, (byte) 0x02, (byte) 0x04, (byte) 0x58, (byte) 0x68, (byte) 0xb8,
//        (byte) 0x04, (byte) 0xb8, (byte) 0x1d, (byte) 0x0d, (byte) 0x0d, (byte) 0x05, (byte) 0x9d, (byte) 0x15,
//        (byte) 0x54, (byte) 0x52, (byte) 0x99, (byte) 0x68, (byte) 0x24, (byte) 0x10, (byte) 0x9a, (byte) 0x35,
//        (byte) 0x61, (byte) 0x35, (byte) 0x51, (byte) 0x02, (byte) 0xd1, (byte) 0x3f, (byte) 0xbe, (byte) 0xf9,
//        (byte) 0x09, (byte) 0x85, (byte) 0x76, (byte) 0xc6, (byte) 0xec, (byte) 0xdf, (byte) 0xc2, (byte) 0xff,
//        (byte) 0x92, (byte) 0x97, (byte) 0x4c, (byte) 0x66, (byte) 0x7e, (byte) 0xf6, (byte) 0xbd, (byte) 0x99,
//        (byte) 0xfd, (byte) 0x3b, (byte) 0x0b, (byte) 0x0f, (byte) 0x0f, (byte) 0x8f, (byte) 0xbf, (byte) 0xa0,
//        (byte) 0x84, (byte) 0xd2, (byte) 0x35, (byte) 0x79, (byte) 0x95, (byte) 0xa5, (byte) 0x81, (byte) 0x40,
//        (byte) 0xc9, (byte) 0x30, (byte) 0x67, (byte) 0xe9, (byte) 0x1a, (byte) 0x15, (byte) 0x54, (byte) 0x9e,
//        (byte) 0x94, (byte) 0x99, (byte) 0x18, (byte) 0x08, (byte) 0x10, (byte) 0x9c, (byte) 0x35, (byte) 0xd0,
//        (byte) 0x58, (byte) 0x37, (byte) 0xd1, (byte) 0x5c, (byte) 0x17, (byte) 0x51, (byte) 0x3c, (byte) 0x17,
//        (byte) 0x48, (byte) 0xce, (byte) 0xa5, (byte) 0xf8, (byte) 0x1e, (byte) 0xc5, (byte) 0x1f, (byte) 0xfa,
//        (byte) 0xe8, (byte) 0x1b, (byte) 0x52, (byte) 0x18, (byte) 0x3f, (byte) 0x46, (byte) 0x88, (byte) 0xf6,
//        (byte) 0x9d, (byte) 0x4d, (byte) 0xa1, (byte) 0x8a, (byte) 0xea, (byte) 0x45, (byte) 0x17, (byte) 0xdd,
//        (byte) 0x97, (byte) 0x11, (byte) 0x46, (byte) 0x86, (byte) 0x94, (byte) 0x1e, (byte) 0x7a, (byte) 0xaf,
//        (byte) 0x35, (byte) 0xd4, (byte) 0x2e, (byte) 0x9d, (byte) 0x4c, (byte) 0x81, (byte) 0xdd, (byte) 0x1f,
//        (byte) 0xb0, (byte) 0xe3, (byte) 0x38, (byte) 0x41, (byte) 0x62, (byte) 0xc5, (byte) 0x95, (byte) 0x8c,
//        (byte) 0xa5, (byte) 0x8e, (byte) 0xfa, (byte) 0x33, (byte) 0xf3, (byte) 0x87, (byte) 0xa9, (byte) 0x4f,
//        (byte) 0x81, (byte) 0xef, (byte) 0x3c, (byte) 0x1c, (byte) 0x62, (byte) 0xf8, (byte) 0x6e, (byte) 0x60,
//        (byte) 0x64, (byte) 0x67, (byte) 0x40, (byte) 0x20, (byte) 0xc2, (byte) 0x9c, (byte) 0xde, (byte) 0x87,
//        (byte) 0x56, (byte) 0xaa, (byte) 0x53, (byte) 0x60, (byte) 0xf7, (byte) 0x47, (byte) 0x14, (byte) 0x59,
//        (byte) 0x6d, (byte) 0xb0, (byte) 0x31, (byte) 0x14, (byte) 0xfa, (byte) 0x66, (byte) 0x80, (byte) 0x39,
//        (byte) 0x61, (byte) 0x2d, (byte) 0xe1, (byte) 0x57, (byte) 0x71, (byte) 0x8c, (byte) 0xb4, (byte) 0x10,
//        (byte) 0x22, (byte) 0xbc, (byte) 0x19, (byte) 0x63, (byte) 0xfc, (byte) 0xc1, (byte) 0xee, (byte) 0xad,
//        (byte) 0x28, (byte) 0x2f, (byte) 0xa0, (byte) 0x52, (byte) 0x63, (byte) 0xcb, (byte) 0x09, (byte) 0x26,
//        (byte) 0x9f, (byte) 0x2d, (byte) 0xb4, (byte) 0x22, (byte) 0xa4, (byte) 0x05, (byte) 0x3e, (byte) 0xfc,
//        (byte) 0x2e, (byte) 0x46, (byte) 0x6c, (byte) 0xd4, (byte) 0xc0, (byte) 0x1c, (byte) 0x73, (byte) 0xe1,
//        (byte) 0x44, (byte) 0x2c, (byte) 0x17, (byte) 0x58, (byte) 0x58, (byte) 0x03, (byte) 0xac, (byte) 0x49,
//        (byte) 0x1b, (byte) 0xed, (byte) 0xe9, (byte) 0xff, (byte) 0x30, (byte) 0x40, (byte) 0x41, (byte) 0x35,
//        (byte) 0xa1, (byte) 0xd4, (byte) 0x38, (byte) 0x13, (byte) 0x03, (byte) 0x3b, (byte) 0x7a, (byte) 0x03,
//        (byte) 0xd9, (byte) 0x19, (byte) 0x58, (byte) 0x62, (byte) 0xa9, (byte) 0x74, (byte) 0x6f, (byte) 0x60,
//        (byte) 0x8b, (byte) 0xad, (byte) 0x0c, (byte) 0x30, (byte) 0x78, (byte) 0xe3, (byte) 0xea, (byte) 0x9d,
//        (byte) 0x71, (byte) 0x05, (byte) 0xcf, (byte) 0x34, (byte) 0x66, (byte) 0xce, (byte) 0x8d, (byte) 0x01,
//        (byte) 0x2e, (byte) 0x1b, (byte) 0x43, (byte) 0x91, (byte) 0x44, (byte) 0xd7, (byte) 0x6e, (byte) 0x07,
//        (byte) 0x9d, (byte) 0x7c, (byte) 0x01, (byte) 0x85, (byte) 0x3c, (byte) 0xe3, (byte) 0x90, (byte) 0xb9,
//        (byte) 0x15, (byte) 0x97, (byte) 0x94, (byte) 0xf0, (byte) 0xcc, (byte) 0x34, (byte) 0xcd, (byte) 0x4d,
//        (byte) 0x78, (byte) 0xcb, (byte) 0xae, (byte) 0xef, (byte) 0x03, (byte) 0x04, (byte) 0xa7, (byte) 0x3f,
//        (byte) 0x7f, (byte) 0x3a, (byte) 0x65, (byte) 0x94, (byte) 0x4f, (byte) 0xb4, (byte) 0xa6, (byte) 0x67,
//        (byte) 0xe0, (byte) 0xe1, (byte) 0xe1, (byte) 0xf1, (byte) 0x0b, (byte) 0x7c, (byte) 0x01, (byte) 0x2d,
//        (byte) 0x46, (byte) 0x70, (byte) 0xf0, (byte) 0x42, (byte) 0xf5, (byte) 0xf9, (byte) 0x03, (byte) 0x00,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae,
//        (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_PURPLE_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x71, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x94, (byte) 0xb1, (byte) 0x0d, (byte) 0x80,
//        (byte) 0x30, (byte) 0x0c, (byte) 0x04, (byte) 0x4d, (byte) 0xcd, (byte) 0x34, (byte) 0x59, (byte) 0x80,
//        (byte) 0x05, (byte) 0x90, (byte) 0xd8, (byte) 0x86, (byte) 0x3e, (byte) 0x35, (byte) 0x9b, (byte) 0x51,
//        (byte) 0x30, (byte) 0x45, (byte) 0xb2, (byte) 0x87, (byte) 0xa3, (byte) 0x6f, (byte) 0x71, (byte) 0xaa,
//        (byte) 0x14, (byte) 0xfe, (byte) 0xe6, (byte) 0x4f, (byte) 0x7a, (byte) 0xc9, (byte) 0xdd, (byte) 0xbd,
//        (byte) 0x94, (byte) 0xd8, (byte) 0x26, (byte) 0x84, (byte) 0x10, (byte) 0x62, (byte) 0x95, (byte) 0xb3,
//        (byte) 0x1c, (byte) 0xd7, (byte) 0xb3, (byte) 0xdf, (byte) 0x8e, (byte) 0x60, (byte) 0xa6, (byte) 0xc8,
//        (byte) 0xbf, (byte) 0xed, (byte) 0x45, (byte) 0x42, (byte) 0x89, (byte) 0x34, (byte) 0xb9, (byte) 0x9b,
//        (byte) 0x23, (byte) 0xa1, (byte) 0x44, (byte) 0xa6, (byte) 0x7c, (byte) 0x5a, (byte) 0x22, (byte) 0x4d,
//        (byte) 0xde, (byte) 0xad, (byte) 0x23, (byte) 0x39, (byte) 0x25, (byte) 0xfe, (byte) 0xf2, (byte) 0x66,
//        (byte) 0xcd, (byte) 0xab, (byte) 0x55, (byte) 0x04, (byte) 0x73, (byte) 0x28, (byte) 0x41, (byte) 0x2f,
//        (byte) 0x90, (byte) 0xf1, (byte) 0x04, (byte) 0x10, (byte) 0x23, (byte) 0xd4, (byte) 0x7f, (byte) 0xc0,
//        (byte) 0xde, (byte) 0x84, (byte) 0x28, (byte) 0xa7, (byte) 0x1e, (byte) 0x22, (byte) 0xee, (byte) 0x29,
//        (byte) 0x16, (byte) 0x42, (byte) 0x08, (byte) 0xb1, (byte) 0xc8, (byte) 0x00, (byte) 0x17, (byte) 0xb5,
//        (byte) 0xeb, (byte) 0x05, (byte) 0xe8, (byte) 0x30, (byte) 0xc1, (byte) 0x9e, (byte) 0x00, (byte) 0x00,
//        (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42,
//        (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_RED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x20, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0xd5, (byte) 0x31, (byte) 0x4a, (byte) 0x03,
//        (byte) 0x41, (byte) 0x18, (byte) 0x86, (byte) 0xe1, (byte) 0x57, (byte) 0x2d, (byte) 0x6c, (byte) 0xf4,
//        (byte) 0x06, (byte) 0xf1, (byte) 0x0c, (byte) 0x7a, (byte) 0x08, (byte) 0xbd, (byte) 0x8a, (byte) 0x56,
//        (byte) 0x1e, (byte) 0x40, (byte) 0x04, (byte) 0x19, (byte) 0x25, (byte) 0x4a, (byte) 0x66, (byte) 0x76,
//        (byte) 0xb7, (byte) 0xf1, (byte) 0x12, (byte) 0x6a, (byte) 0xba, (byte) 0x34, (byte) 0x36, (byte) 0x29,
//        (byte) 0xec, (byte) 0xac, (byte) 0x92, (byte) 0x4a, (byte) 0x2b, (byte) 0x41, (byte) 0x51, (byte) 0x14,
//        (byte) 0x9b, (byte) 0x15, (byte) 0xb2, (byte) 0x0a, (byte) 0x1a, (byte) 0x12, (byte) 0x74, (byte) 0xc6,
//        (byte) 0x19, (byte) 0xf8, (byte) 0xab, (byte) 0x74, (byte) 0x12, (byte) 0x76, (byte) 0xb6, (byte) 0x70,
//        (byte) 0x3e, (byte) 0x78, (byte) 0x60, (byte) 0x61, (byte) 0x17, (byte) 0xe6, (byte) 0x65, (byte) 0x9a,
//        (byte) 0x25, (byte) 0x2d, (byte) 0x2d, (byte) 0x6d, (byte) 0x9e, (byte) 0x1d, (byte) 0xc2, (byte) 0xbe,
//        (byte) 0xb7, (byte) 0xd7, (byte) 0x64, (byte) 0x80, (byte) 0x0a, (byte) 0x80, (byte) 0x05, (byte) 0x11,
//        (byte) 0x77, (byte) 0x6d, (byte) 0x78, (byte) 0x0c, (byte) 0x1a, (byte) 0x09, (byte) 0x50, (byte) 0xb0,
//        (byte) 0xa9, (byte) 0x61, (byte) 0x6c, (byte) 0xbc, (byte) 0x03, (byte) 0xd8, (byte) 0x72, (byte) 0x31,
//        (byte) 0x03, (byte) 0x14, (byte) 0x2c, (byte) 0x6a, (byte) 0xb8, (byte) 0xef, (byte) 0x81, (byte) 0xf5,
//        (byte) 0x9c, (byte) 0x86, (byte) 0x87, (byte) 0x2e, (byte) 0x2c, (byte) 0x11, (byte) 0x2b, (byte) 0xe2,
//        (byte) 0x18, (byte) 0xb6, (byte) 0xcf, (byte) 0xe0, (byte) 0xfd, (byte) 0x0a, (byte) 0xac, (byte) 0xe7,
//        (byte) 0xce, (byte) 0xe1, (byte) 0xe3, (byte) 0x04, (byte) 0x76, (byte) 0xa2, (byte) 0xdc, (byte) 0x82,
//        (byte) 0x82, (byte) 0x15, (byte) 0x0d, (byte) 0x65, (byte) 0x25, (byte) 0x87, (byte) 0x07, (byte) 0x95,
//        (byte) 0xd7, (byte) 0x81, (byte) 0x37, (byte) 0x0d, (byte) 0xab, (byte) 0xd4, (byte) 0x1d, (byte) 0x61,
//        (byte) 0x20, (byte) 0xeb, (byte) 0xc3, (byte) 0x97, (byte) 0x0d, (byte) 0x87, (byte) 0x0b, (byte) 0xe7,
//        (byte) 0xf5, (byte) 0x61, (byte) 0x6c, (byte) 0x20, (byte) 0xaf, (byte) 0xf5, (byte) 0x16, (byte) 0x14,
//        (byte) 0xb4, (byte) 0x0c, (byte) 0x8c, (byte) 0x26, (byte) 0x60, (byte) 0xdd, (byte) 0x4c, (byte) 0xc0,
//        (byte) 0xc4, (byte) 0x33, (byte) 0x50, (byte) 0xb5, (byte) 0x61, (byte) 0x8d, (byte) 0xba, (byte) 0x96,
//        (byte) 0xc1, (byte) 0xc5, (byte) 0x00, (byte) 0xbe, (byte) 0xad, (byte) 0x1c, (byte) 0xda, (byte) 0x13,
//        (byte) 0x4e, (byte) 0x0c, (byte) 0xe1, (byte) 0x27, (byte) 0x87, (byte) 0x2e, (byte) 0x75, (byte) 0x2d,
//        (byte) 0x87, (byte) 0x9b, (byte) 0x12, (byte) 0x6c, (byte) 0x08, (byte) 0x78, (byte) 0xf6, (byte) 0x94,
//        (byte) 0x78, (byte) 0x91, (byte) 0x80, (byte) 0xd2, (byte) 0x2b, (byte) 0xe0, (byte) 0xf6, (byte) 0x7f,
//        (byte) 0x04, (byte) 0xb8, (byte) 0x10, (byte) 0x21, (byte) 0x5c, (byte) 0x33, (byte) 0x01, (byte) 0x22,
//        (byte) 0x05, (byte) 0x34, (byte) 0x19, (byte) 0xf0, (byte) 0x2a, (byte) 0xa2, (byte) 0x07, (byte) 0x4c,
//        (byte) 0xc1, (byte) 0x5d, (byte) 0xc2, (byte) 0xa7, (byte) 0x86, (byte) 0xa7, (byte) 0x8e, (byte) 0x17,
//        (byte) 0x9e, (byte) 0xa7, (byte) 0xb1, (byte) 0x02, (byte) 0x86, (byte) 0x60, (byte) 0x0b, (byte) 0xa8,
//        (byte) 0x0c, (byte) 0xe4, (byte) 0xa7, (byte) 0xb0, (byte) 0xbc, (byte) 0xeb, (byte) 0x19, (byte) 0xc8,
//        (byte) 0x0a, (byte) 0x18, (byte) 0x0d, (byte) 0xc0, (byte) 0xe5, (byte) 0x75, (byte) 0x06, (byte) 0x64,
//        (byte) 0x70, (byte) 0xad, (byte) 0xe1, (byte) 0x4e, (byte) 0xc1, (byte) 0xc6, (byte) 0xec, (byte) 0x4f,
//        (byte) 0xe7, (byte) 0x08, (byte) 0xd6, (byte) 0xc3, (byte) 0xbb, (byte) 0xf0, (byte) 0x0d, (byte) 0x69,
//        (byte) 0x69, (byte) 0x69, (byte) 0x7f, (byte) 0xd8, (byte) 0x2f, (byte) 0x4f, (byte) 0x4b, (byte) 0xfa,
//        (byte) 0x55, (byte) 0xb3, (byte) 0x84, (byte) 0x6b, (byte) 0x5f, (byte) 0x00, (byte) 0x00, (byte) 0x00,
//        (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42, (byte) 0x60,
//        (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_RED_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x6c, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x58, (byte) 0xc3, (byte) 0x63, (byte) 0x60, (byte) 0x18, (byte) 0x05, (byte) 0xa3,
//        (byte) 0x60, (byte) 0x14, (byte) 0x8c, (byte) 0x82, (byte) 0x51, (byte) 0x40, (byte) 0x26, (byte) 0x08,
//        (byte) 0xd5, (byte) 0x30, (byte) 0x88, (byte) 0xea, (byte) 0x64, (byte) 0x14, (byte) 0xf8, (byte) 0x0f,
//        (byte) 0xc2, (byte) 0x20, (byte) 0xf6, (byte) 0x80, (byte) 0x58, (byte) 0x7e, (byte) 0x96, (byte) 0x81,
//        (byte) 0x19, (byte) 0x8c, (byte) 0xe9, (byte) 0xea, (byte) 0x08, (byte) 0x64, (byte) 0xcb, (byte) 0xff,
//        (byte) 0x33, (byte) 0x30, (byte) 0x80, (byte) 0x31, (byte) 0xdd, (byte) 0x1c, (byte) 0x81, (byte) 0xcd,
//        (byte) 0x72, (byte) 0xba, (byte) 0x39, (byte) 0x02, (byte) 0x9b, (byte) 0xe5, (byte) 0x4f, (byte) 0xa0,
//        (byte) 0x98, (byte) 0x2e, (byte) 0x8e, (byte) 0x40, (byte) 0xb7, (byte) 0xfc, (byte) 0x31, (byte) 0x10,
//        (byte) 0x37, (byte) 0x40, (byte) 0xf1, (byte) 0x63, (byte) 0x2c, (byte) 0x8e, (byte) 0x18, (byte) 0x7e,
//        (byte) 0x0e, (byte) 0xc0, (byte) 0x16, (byte) 0x05, (byte) 0x8f, (byte) 0x71, (byte) 0x58, (byte) 0x4e,
//        (byte) 0xd7, (byte) 0x74, (byte) 0x30, (byte) 0x28, (byte) 0x72, (byte) 0xc2, (byte) 0x80, (byte) 0x96,
//        (byte) 0x05, (byte) 0x74, (byte) 0xb7, (byte) 0x7c, (byte) 0x50, (byte) 0x14, (byte) 0xc5, (byte) 0xa3,
//        (byte) 0x60, (byte) 0x14, (byte) 0x8c, (byte) 0x82, (byte) 0x51, (byte) 0x30, (byte) 0xac, (byte) 0x00,
//        (byte) 0x00, (byte) 0x34, (byte) 0xc6, (byte) 0xab, (byte) 0xe7, (byte) 0x46, (byte) 0x95, (byte) 0xa5,
//        (byte) 0x54, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e,
//        (byte) 0x44, (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_TURQUOISE_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x49, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x95, (byte) 0xb1, (byte) 0x4a, (byte) 0x03,
//        (byte) 0x41, (byte) 0x14, (byte) 0x45, (byte) 0x6f, (byte) 0xb4, (byte) 0x48, (byte) 0xa3, (byte) 0x7f,
//        (byte) 0xa0, (byte) 0xdf, (byte) 0xa0, (byte) 0x1f, (byte) 0xa1, (byte) 0xbf, (byte) 0xa2, (byte) 0x55,
//        (byte) 0x3e, (byte) 0x40, (byte) 0x84, (byte) 0x30, (byte) 0x09, (byte) 0x49, (byte) 0x48, (byte) 0x76,
//        (byte) 0x37, (byte) 0x4d, (byte) 0x7e, (byte) 0x22, (byte) 0x71, (byte) 0xbb, (byte) 0x6d, (byte) 0xd2,
//        (byte) 0xa4, (byte) 0x48, (byte) 0x67, (byte) 0x95, (byte) 0xad, (byte) 0x4c, (byte) 0x25, (byte) 0x28,
//        (byte) 0x86, (byte) 0x84, (byte) 0x34, (byte) 0x2b, (byte) 0x64, (byte) 0x15, (byte) 0x74, (byte) 0x49,
//        (byte) 0xd0, (byte) 0x79, (byte) 0x5e, (byte) 0x66, (byte) 0x2a, (byte) 0xb1, (byte) 0x12, (byte) 0xd9,
//        (byte) 0xdd, (byte) 0xc2, (byte) 0xb9, (byte) 0x70, (byte) 0xe0, (byte) 0xf1, (byte) 0x66, (byte) 0xe0,
//        (byte) 0xde, (byte) 0x79, (byte) 0xb0, (byte) 0x6f, (byte) 0xe1, (byte) 0xe4, (byte) 0xe4, (byte) 0xf4,
//        (byte) 0x27, (byte) 0x35, (byte) 0x1a, (byte) 0xd7, (byte) 0xe4, (byte) 0xaa, (byte) 0xcc, (byte) 0x00,
//        (byte) 0xca, (byte) 0x00, (byte) 0x54, (byte) 0x0c, (byte) 0x85, (byte) 0xab, (byte) 0xd5, (byte) 0x7a,
//        (byte) 0x32, (byte) 0x94, (byte) 0x12, (byte) 0x40, (byte) 0xa9, (byte) 0x33, (byte) 0xf4, (byte) 0x7a,
//        (byte) 0x19, (byte) 0x3c, (byte) 0x2f, (byte) 0x43, (byte) 0xbd, (byte) 0x7e, (byte) 0x0e, (byte) 0x91,
//        (byte) 0x4a, (byte) 0x91, (byte) 0xe6, (byte) 0x7b, (byte) 0x34, (byte) 0x7f, (byte) 0x40, (byte) 0x14,
//        (byte) 0x69, (byte) 0x22, (byte) 0xac, (byte) 0x1f, (byte) 0x11, (byte) 0x86, (byte) 0xfb, (byte) 0xc5,
//        (byte) 0x4d, (byte) 0xa1, (byte) 0xdd, (byte) 0xbe, (byte) 0xc0, (byte) 0x70, (byte) 0xf8, (byte) 0x82,
//        (byte) 0xe9, (byte) 0x54, (byte) 0x13, (byte) 0xc1, (byte) 0x68, (byte) 0xf4, (byte) 0x8a, (byte) 0x4e,
//        (byte) 0xe7, (byte) 0xb2, (byte) 0x98, (byte) 0x29, (byte) 0x28, (byte) 0x75, (byte) 0xc0, (byte) 0x17,
//        (byte) 0x27, (byte) 0x48, (byte) 0x53, (byte) 0x6b, (byte) 0x4e, (byte) 0x58, (byte) 0x0b, (byte) 0xba,
//        (byte) 0xdd, (byte) 0x67, (byte) 0xf6, (byte) 0x0f, (byte) 0xf3, (byte) 0x9f, (byte) 0x82, (byte) 0xe7,
//        (byte) 0xf9, (byte) 0x98, (byte) 0x4c, (byte) 0xde, (byte) 0xa1, (byte) 0xb5, (byte) 0x31, (byte) 0x37,
//        (byte) 0x88, (byte) 0x08, (byte) 0x7b, (byte) 0x19, (byte) 0xcf, (byte) 0x82, (byte) 0x7c, (byte) 0xa7,
//        (byte) 0xa0, (byte) 0xd4, (byte) 0x11, (byte) 0x4d, (byte) 0x36, (byte) 0xd8, (byte) 0x6e, (byte) 0x35,
//        (byte) 0x8d, (byte) 0xbe, (byte) 0x05, (byte) 0x60, (byte) 0x4f, (byte) 0x78, (byte) 0x96, (byte) 0xf2,
//        (byte) 0xab, (byte) 0x38, (byte) 0x46, (byte) 0x6e, (byte) 0xf2, (byte) 0xfd, (byte) 0x1b, (byte) 0xcc,
//        (byte) 0x66, (byte) 0x1f, (byte) 0xd0, (byte) 0xda, (byte) 0x9a, (byte) 0x46, (byte) 0x91, (byte) 0x45,
//        (byte) 0xc4, (byte) 0x12, (byte) 0xc7, (byte) 0x9f, (byte) 0x08, (byte) 0x82, (byte) 0x10, (byte) 0xb9,
//        (byte) 0x29, (byte) 0x08, (byte) 0xee, (byte) 0x90, (byte) 0x24, (byte) 0xda, (byte) 0x04, (byte) 0x58,
//        (byte) 0x2e, (byte) 0x05, (byte) 0x4a, (byte) 0x59, (byte) 0x56, (byte) 0x2b, (byte) 0x13, (byte) 0x80,
//        (byte) 0x67, (byte) 0x82, (byte) 0x7e, (byte) 0x7f, (byte) 0xfe, (byte) 0x4f, (byte) 0x02, (byte) 0x88,
//        (byte) 0xd8, (byte) 0x10, (byte) 0x84, (byte) 0x75, (byte) 0x49, (byte) 0x01, (byte) 0x2c, (byte) 0x2e,
//        (byte) 0x40, (byte) 0x89, (byte) 0x01, (byte) 0xd6, (byte) 0x6b, (byte) 0x43, (byte) 0xf1, (byte) 0x01,
//        (byte) 0x76, (byte) 0x3b, (byte) 0xc1, (byte) 0x78, (byte) 0xfc, (byte) 0xc6, (byte) 0xd5, (byte) 0xbb,
//        (byte) 0xe0, (byte) 0x0a, (byte) 0x5e, (byte) 0x98, (byte) 0x9a, (byte) 0xbd, (byte) 0x62, (byte) 0x02,
//        (byte) 0xc4, (byte) 0xb1, (byte) 0xa6, (byte) 0x49, (byte) 0x6a, (byte) 0xd6, (byte) 0xee, (byte) 0x60,
//        (byte) 0x50, (byte) 0x45, (byte) 0xad, (byte) 0x56, (byte) 0x65, (byte) 0xed, (byte) 0xb3, (byte) 0xb7,
//        (byte) 0xe1, (byte) 0x92, (byte) 0x12, (byte) 0xde, (byte) 0x99, (byte) 0xe7, (byte) 0xb9, (byte) 0x09,
//        (byte) 0x6f, (byte) 0xf9, (byte) 0xea, (byte) 0x7b, (byte) 0x28, (byte) 0x75, (byte) 0xfa, (byte) 0xe3,
//        (byte) 0xa7, (byte) 0xd3, (byte) 0x6c, (byte) 0x9e, (byte) 0x80, (byte) 0x67, (byte) 0xe6, (byte) 0x8e,
//        (byte) 0x93, (byte) 0x93, (byte) 0xd3, (byte) 0x2f, (byte) 0xf4, (byte) 0x05, (byte) 0xb6, (byte) 0xab,
//        (byte) 0x70, (byte) 0xf0, (byte) 0xc5, (byte) 0xc5, (byte) 0x2d, (byte) 0x4d, (byte) 0x00, (byte) 0x00,
//        (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42,
//        (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_TURQUOISE_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x6e, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x94, (byte) 0xb1, (byte) 0x09, (byte) 0xc0,
//        (byte) 0x30, (byte) 0x0c, (byte) 0x04, (byte) 0x05, (byte) 0x1e, (byte) 0x21, (byte) 0xc3, (byte) 0xa4,
//        (byte) 0x74, (byte) 0x97, (byte) 0x51, (byte) 0xb2, (byte) 0x41, (byte) 0x48, (byte) 0xeb, (byte) 0xf5,
//        (byte) 0x3c, (byte) 0x87, (byte) 0xbc, (byte) 0x87, (byte) 0xc2, (byte) 0xd7, (byte) 0x4a, (byte) 0xe5,
//        (byte) 0x42, (byte) 0xdf, (byte) 0xfc, (byte) 0xc1, (byte) 0x81, (byte) 0xba, (byte) 0x7f, (byte) 0xb0,
//        (byte) 0x25, (byte) 0x13, (byte) 0x42, (byte) 0x08, (byte) 0xb1, (byte) 0xcb, (byte) 0x75, (byte) 0xf6,
//        (byte) 0xfb, (byte) 0x78, (byte) 0xde, (byte) 0x80, (byte) 0x98, (byte) 0x29, (byte) 0xe1, (byte) 0x6d,
//        (byte) 0x4e, (byte) 0x98, (byte) 0x4a, (byte) 0x94, (byte) 0x85, (byte) 0x5b, (byte) 0x04, (byte) 0x4c,
//        (byte) 0x25, (byte) 0x2a, (byte) 0xc3, (byte) 0x7f, (byte) 0x4b, (byte) 0xd4, (byte) 0x85, (byte) 0xaf,
//        (byte) 0x05, (byte) 0x6b, (byte) 0x4a, (byte) 0xa4, (byte) 0x70, (byte) 0xf7, (byte) 0xb0, (byte) 0x31,
//        (byte) 0x20, (byte) 0xe6, (byte) 0x54, (byte) 0x82, (byte) 0x5e, (byte) 0xa0, (byte) 0xe6, (byte) 0x09,
//        (byte) 0xdc, (byte) 0x21, (byte) 0xf5, (byte) 0x1f, (byte) 0xb0, (byte) 0x37, (byte) 0x21, (byte) 0x87,
//        (byte) 0x53, (byte) 0x0f, (byte) 0x11, (byte) 0xf7, (byte) 0x14, (byte) 0x0b, (byte) 0x21, (byte) 0x84,
//        (byte) 0xd8, (byte) 0xe4, (byte) 0x03, (byte) 0xa1, (byte) 0xa5, (byte) 0xeb, (byte) 0xdd, (byte) 0xa2,
//        (byte) 0x70, (byte) 0x5e, (byte) 0x2d, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49,
//        (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_WHITE_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x04, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0xd9, (byte) 0x73, (byte) 0xb2,
//        (byte) 0x7f, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x17, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0x01, (byte) 0x63, (byte) 0x18, (byte) 0x04, (byte) 0x60, (byte) 0x14,
//        (byte) 0x30, (byte) 0xe3, (byte) 0x97, (byte) 0x36, (byte) 0xa9, (byte) 0x96, (byte) 0xb4, (byte) 0x91,
//        (byte) 0x3a, (byte) 0x22, (byte) 0xc5, (byte) 0x80, (byte) 0x1b, (byte) 0xb2, (byte) 0x18, (byte) 0xe3,
//        (byte) 0x37, (byte) 0x81, (byte) 0x95, (byte) 0x91, (byte) 0xe1, (byte) 0x0c, (byte) 0x23, (byte) 0x90,
//        (byte) 0xfe, (byte) 0x8f, (byte) 0x4b, (byte) 0x01, (byte) 0xd3, (byte) 0x59, (byte) 0x06, (byte) 0x7c,
//        (byte) 0x90, (byte) 0x25, (byte) 0x81, (byte) 0x25, (byte) 0x01, (byte) 0xbf, (byte) 0x0d, (byte) 0x4c,
//        (byte) 0xf8, (byte) 0x24, (byte) 0x4d, (byte) 0x5d, (byte) 0x38, (byte) 0xc4, (byte) 0x39, (byte) 0xc4,
//        (byte) 0x4d, (byte) 0x5c, (byte) 0xc8, (byte) 0x35, (byte) 0x80, (byte) 0x89, (byte) 0x63, (byte) 0xa6,
//        (byte) 0x1e, (byte) 0xbb, (byte) 0x1e, (byte) 0x07, (byte) 0xe7, (byte) 0x4c, (byte) 0xa0, (byte) 0x2a,
//        (byte) 0x46, (byte) 0x32, (byte) 0xc2, (byte) 0xd7, (byte) 0x3c, (byte) 0x2d, (byte) 0xe9, (byte) 0xe3,
//        (byte) 0x7f, (byte) 0x20, (byte) 0x48, (byte) 0xfa, (byte) 0x64, (byte) 0x96, (byte) 0x4e, (byte) 0x86,
//        (byte) 0x01, (byte) 0xda, (byte) 0x3c, (byte) 0xb6, (byte) 0xaf, (byte) 0x77, (byte) 0xfd, (byte) 0x03,
//        (byte) 0x19, (byte) 0xb0, (byte) 0xeb, (byte) 0xbf, (byte) 0xed, (byte) 0x1b, (byte) 0x6b, (byte) 0x5e,
//        (byte) 0x06, (byte) 0x46, (byte) 0x12, (byte) 0xbd, (byte) 0x20, (byte) 0x54, (byte) 0x6f, (byte) 0xcb,
//        (byte) 0xed, (byte) 0x0a, (byte) 0xd6, (byte) 0xe4, (byte) 0xca, (byte) 0x60, (byte) 0xcb, (byte) 0xcd,
//        (byte) 0x5c, (byte) 0x4f, (byte) 0xaa, (byte) 0xf3, (byte) 0x65, (byte) 0x1c, (byte) 0xde, (byte) 0xff,
//        (byte) 0x47, (byte) 0x02, (byte) 0x8e, (byte) 0x1f, (byte) 0xec, (byte) 0x64, (byte) 0xed, (byte) 0x18,
//        (byte) 0xb0, (byte) 0x41, (byte) 0x26, (byte) 0xec, (byte) 0xc2, (byte) 0x5c, (byte) 0xbd, (byte) 0x01,
//        (byte) 0xbc, (byte) 0x20, (byte) 0x83, (byte) 0xf2, (byte) 0x81, (byte) 0x10, (byte) 0x04, (byte) 0xfc,
//        (byte) 0x79, (byte) 0x59, (byte) 0x7a, (byte) 0x59, (byte) 0x18, (byte) 0xb0, (byte) 0x41, (byte) 0x26,
//        (byte) 0xec, (byte) 0xc2, (byte) 0x0c, (byte) 0x5a, (byte) 0x2a, (byte) 0x40, (byte) 0xcf, (byte) 0xad,
//        (byte) 0x66, (byte) 0x38, (byte) 0xc6, (byte) 0x70, (byte) 0x94, (byte) 0x61, (byte) 0x0d, (byte) 0xd0,
//        (byte) 0x00, (byte) 0x20, (byte) 0x4f, (byte) 0x0b, (byte) 0x57, (byte) 0x18, (byte) 0x50, (byte) 0x08,
//        (byte) 0x58, (byte) 0xf0, (byte) 0x49, (byte) 0x86, (byte) 0x82, (byte) 0xc9, (byte) 0x10, (byte) 0x32,
//        (byte) 0x0d, (byte) 0x40, (byte) 0x18, (byte) 0x81, (byte) 0x00, (byte) 0x94, (byte) 0x7b, (byte) 0x81,
//        (byte) 0xfe, (byte) 0x06, (byte) 0xac, (byte) 0x03, (byte) 0x42, (byte) 0x0a, (byte) 0x02, (byte) 0xb1,
//        (byte) 0xec, (byte) 0xdb, (byte) 0xc9, (byte) 0x37, (byte) 0xff, (byte) 0x19, (byte) 0x4e, (byte) 0x88,
//        (byte) 0x74, (byte) 0x71, (byte) 0x91, (byte) 0xe1, (byte) 0x82, (byte) 0x3b, (byte) 0x0c, (byte) 0xae,
//        (byte) 0x1f, (byte) 0x8f, (byte) 0x4f, (byte) 0x7f, (byte) 0xa6, (byte) 0xf6, (byte) 0x5c, (byte) 0xed,
//        (byte) 0xc4, (byte) 0x34, (byte) 0xd7, (byte) 0x0f, (byte) 0x77, (byte) 0x18, (byte) 0xfe, (byte) 0xe3,
//        (byte) 0x80, (byte) 0x8c, (byte) 0x8e, (byte) 0x58, (byte) 0xb5, (byte) 0xff, (byte) 0x3b, (byte) 0xf6,
//        (byte) 0x4f, (byte) 0xe4, (byte) 0x73, (byte) 0xd8, (byte) 0x85, (byte) 0x8b, (byte) 0x90, (byte) 0x92,
//        (byte) 0xc8, (byte) 0x50, (byte) 0x9f, (byte) 0x67, (byte) 0x35, (byte) 0xd3, (byte) 0x1b, (byte) 0x06,
//        (byte) 0x2b, (byte) 0xac, (byte) 0x4a, (byte) 0xed, (byte) 0x29, (byte) 0x83, (byte) 0xd4, (byte) 0x30,
//        (byte) 0x60, (byte) 0xd4, (byte) 0x00, (byte) 0x00, (byte) 0x1f, (byte) 0x65, (byte) 0x99, (byte) 0xb9,
//        (byte) 0x73, (byte) 0x31, (byte) 0x1f, (byte) 0xc4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00,
//        (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_WHITE_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x7b, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x95, (byte) 0xb1, (byte) 0x09, (byte) 0xc0,
//        (byte) 0x30, (byte) 0x0c, (byte) 0x04, (byte) 0xd3, (byte) 0x64, (byte) 0x81, (byte) 0xac, (byte) 0xe2,
//        (byte) 0xca, (byte) 0x85, (byte) 0x76, (byte) 0xf4, (byte) 0x1c, (byte) 0xf2, (byte) 0x46, (byte) 0x59,
//        (byte) 0x25, (byte) 0x85, (byte) 0x93, (byte) 0x07, (byte) 0x57, (byte) 0x51, (byte) 0xda, (byte) 0xf8,
//        (byte) 0x11, (byte) 0xfc, (byte) 0xc1, (byte) 0x83, (byte) 0x30, (byte) 0x98, (byte) 0x7f, (byte) 0x90,
//        (byte) 0x2c, (byte) 0x6f, (byte) 0x42, (byte) 0x08, (byte) 0x91, (byte) 0x9a, (byte) 0x52, (byte) 0xca,
//        (byte) 0x0e, (byte) 0x51, (byte) 0xcc, (byte) 0x6b, (byte) 0xad, (byte) 0x87, (byte) 0x99, (byte) 0x9d,
//        (byte) 0x10, (byte) 0x6a, (byte) 0x8a, (byte) 0x79, (byte) 0x6b, (byte) 0xed, (byte) 0x82, (byte) 0x42,
//        (byte) 0x88, (byte) 0x55, (byte) 0xe6, (byte) 0x63, (byte) 0x12, (byte) 0x42, (byte) 0xac, (byte) 0x34,
//        (byte) 0xff, (byte) 0x0a, (byte) 0xb1, (byte) 0xcc, (byte) 0xbc, (byte) 0xf7, (byte) 0x0e, (byte) 0xfd,
//        (byte) 0x1f, (byte) 0x02, (byte) 0x93, (byte) 0xfe, (byte) 0x36, (byte) 0x77, (byte) 0xf7, (byte) 0xf1,
//        (byte) 0x9c, (byte) 0x43, (byte) 0xa8, (byte) 0x43, (byte) 0x08, (byte) 0xdc, (byte) 0x49, (byte) 0x1f,
//        (byte) 0x20, (byte) 0xb6, (byte) 0x20, (byte) 0x86, (byte) 0x80, (byte) 0xb8, (byte) 0x73, (byte) 0x40,
//        (byte) 0x7e, (byte) 0x09, (byte) 0xd1, (byte) 0x9c, (byte) 0xba, (byte) 0x88, (byte) 0xa8, (byte) 0xab,
//        (byte) 0x98, (byte) 0xfb, (byte) 0x19, (byte) 0x09, (byte) 0x21, (byte) 0x44, (byte) 0x66, (byte) 0x6e,
//        (byte) 0x5f, (byte) 0xb0, (byte) 0xfc, (byte) 0x87, (byte) 0x4a, (byte) 0x35, (byte) 0xde, (byte) 0x46,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44,
//        (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_YELLOW_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x01, (byte) 0x50, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0xed, (byte) 0x95, (byte) 0xbd, (byte) 0x4a, (byte) 0xc3,
//        (byte) 0x60, (byte) 0x14, (byte) 0x86, (byte) 0xbf, (byte) 0x56, (byte) 0xa4, (byte) 0x0a, (byte) 0x0a,
//        (byte) 0x5d, (byte) 0x44, (byte) 0x29, (byte) 0xea, (byte) 0xa4, (byte) 0x4d, (byte) 0xd2, (byte) 0x8a,
//        (byte) 0x7a, (byte) 0x11, (byte) 0x7a, (byte) 0x2b, (byte) 0x3a, (byte) 0x79, (byte) 0x01, (byte) 0x2a,
//        (byte) 0xd6, (byte) 0x60, (byte) 0x85, (byte) 0xfe, (byte) 0xba, (byte) 0x78, (byte) 0x11, (byte) 0x82,
//        (byte) 0x9d, (byte) 0x74, (byte) 0x70, (byte) 0x71, (byte) 0x71, (byte) 0x73, (byte) 0xb2, (byte) 0x93,
//        (byte) 0x4e, (byte) 0x82, (byte) 0xa2, (byte) 0x88, (byte) 0x53, (byte) 0x85, (byte) 0x56, (byte) 0x41,
//        (byte) 0xa5, (byte) 0xa0, (byte) 0x79, (byte) 0x7c, (byte) 0x49, (byte) 0xa1, (byte) 0xa0, (byte) 0x9b,
//        (byte) 0x48, (byte) 0x92, (byte) 0xc1, (byte) 0xbc, (byte) 0xf0, (byte) 0xc0, (byte) 0xe1, (byte) 0x7c,
//        (byte) 0x81, (byte) 0xf3, (byte) 0xe4, (byte) 0x10, (byte) 0xbe, (byte) 0x98, (byte) 0x38, (byte) 0x71,
//        (byte) 0xe2, (byte) 0xfc, (byte) 0x25, (byte) 0x14, (byte) 0x9c, (byte) 0x4d, (byte) 0x0a, (byte) 0xb9,
//        (byte) 0xf5, (byte) 0xe8, (byte) 0x04, (byte) 0xdc, (byte) 0x9c, (byte) 0xeb, (byte) 0x63, (byte) 0x4c,
//        (byte) 0x02, (byte) 0x11, (byte) 0xbe, (byte) 0x40, (byte) 0xd1, (byte) 0xb9, (byte) 0x43, (byte) 0x44,
//        (byte) 0x22, (byte) 0x80, (byte) 0xeb, (byte) 0x2c, (byte) 0x51, (byte) 0xb6, (byte) 0xde, (byte) 0xa9,
//        (byte) 0x88, (byte) 0x82, (byte) 0xb3, (byte) 0x0c, (byte) 0x21, (byte) 0x0a, (byte) 0xe0, (byte) 0x9a,
//        (byte) 0xa4, (byte) 0x86, (byte) 0xdf, (byte) 0x70, (byte) 0x94, (byte) 0xf1, (byte) 0x04, (byte) 0xaa,
//        (byte) 0x6f, (byte) 0x69, (byte) 0x98, (byte) 0x81, (byte) 0xd0, (byte) 0xb6, (byte) 0xc0, (byte) 0x8e,
//        (byte) 0xbd, (byte) 0xc2, (byte) 0xc1, (byte) 0xf4, (byte) 0x33, (byte) 0x67, (byte) 0x63, (byte) 0x9e,
//        (byte) 0x40, (byte) 0xf5, (byte) 0x0b, (byte) 0x45, (byte) 0x7b, (byte) 0x35, (byte) 0x94, (byte) 0x2d,
//        (byte) 0xe0, (byte) 0xe6, (byte) 0x47, (byte) 0x28, (byte) 0xdb, (byte) 0x2d, (byte) 0x3a, (byte) 0x83,
//        (byte) 0xbd, (byte) 0xe1, (byte) 0x42, (byte) 0x35, (byte) 0x94, (byte) 0xec, (byte) 0x27, (byte) 0x2a,
//        (byte) 0xd6, (byte) 0x68, (byte) 0xe0, (byte) 0x5b, (byte) 0xd0, (byte) 0xf0, (byte) 0x1a, (byte) 0xa7,
//        (byte) 0xe3, (byte) 0x6f, (byte) 0x78, (byte) 0x86, (byte) 0xbe, (byte) 0x00, (byte) 0x06, (byte) 0xf5,
//        (byte) 0xf4, (byte) 0x3d, (byte) 0xd8, (byte) 0xf5, (byte) 0x40, (byte) 0xb7, (byte) 0x80, (byte) 0x3b,
//        (byte) 0x3f, (byte) 0x49, (byte) 0x25, (byte) 0xdb, (byte) 0xa6, (byte) 0x9b, (byte) 0xf4, (byte) 0xe0,
//        (byte) 0xbb, (byte) 0x80, (byte) 0x7a, (byte) 0xe8, (byte) 0xac, (byte) 0xc3, (byte) 0xee, (byte) 0xdc,
//        (byte) 0x54, (byte) 0x70, (byte) 0x02, (byte) 0x35, (byte) 0xeb, (byte) 0x90, (byte) 0x8b, (byte) 0xf4,
//        (byte) 0x87, (byte) 0xff, (byte) 0xf6, (byte) 0x88, (byte) 0xe3, (byte) 0x8c, (byte) 0x8f, (byte) 0xea,
//        (byte) 0x1e, (byte) 0xcd, (byte) 0xf4, (byte) 0x27, (byte) 0x75, (byte) 0xab, (byte) 0x11, (byte) 0xa4,
//        (byte) 0xc0, (byte) 0x25, (byte) 0xad, (byte) 0x94, (byte) 0xe7, (byte) 0x0b, (byte) 0x3c, (byte) 0x0c,
//        (byte) 0x83, (byte) 0x9b, (byte) 0x83, (byte) 0x6d, (byte) 0xa1, (byte) 0x1a, (byte) 0x0c, (byte) 0x3a,
//        (byte) 0x83, (byte) 0x7a, (byte) 0xf6, (byte) 0xea, (byte) 0x9f, (byte) 0x08, (byte) 0xe0, (byte) 0x4b,
//        (byte) 0xf8, (byte) 0xa8, (byte) 0x8e, (byte) 0x42, (byte) 0xa0, (byte) 0x4f, (byte) 0x2c, (byte) 0x10,
//        (byte) 0xa1, (byte) 0xc0, (byte) 0xe3, (byte) 0x10, (byte) 0x22, (byte) 0x02, (byte) 0x81, (byte) 0x6e,
//        (byte) 0x02, (byte) 0x4e, (byte) 0x26, (byte) 0x5e, (byte) 0x29, (byte) 0xdb, (byte) 0xf7, (byte) 0x94,
//        (byte) 0x84, (byte) 0x6a, (byte) 0xf5, (byte) 0x42, (byte) 0x12, (byte) 0x68, (byte) 0xa6, (byte) 0x3d,
//        (byte) 0xf6, (byte) 0x66, (byte) 0x3b, (byte) 0xfe, (byte) 0xb5, (byte) 0xbb, (byte) 0x3f, (byte) 0x93,
//        (byte) 0x62, (byte) 0x4d, (byte) 0xe8, (byte) 0x7a, (byte) 0x56, (byte) 0xaf, (byte) 0xad, (byte) 0x4b,
//        (byte) 0x0a, (byte) 0xaa, (byte) 0x41, (byte) 0x0a, (byte) 0x54, (byte) 0xad, (byte) 0x73, (byte) 0x0d,
//        (byte) 0xbb, (byte) 0x66, (byte) 0x23, (byte) 0xbf, (byte) 0xf8, (byte) 0xf3, (byte) 0xa7, (byte) 0xc3,
//        (byte) 0x56, (byte) 0x7e, (byte) 0xc1, (byte) 0x3f, (byte) 0xd3, (byte) 0x33, (byte) 0x26, (byte) 0x4e,
//        (byte) 0x9c, (byte) 0x38, (byte) 0xbf, (byte) 0xc8, (byte) 0x17, (byte) 0x17, (byte) 0x16, (byte) 0xa9,
//        (byte) 0xbd, (byte) 0x86, (byte) 0x0b, (byte) 0x7d, (byte) 0xfa, (byte) 0x00, (byte) 0x00, (byte) 0x00,
//        (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42, (byte) 0x60,
//        (byte) 0x82
//    };
//
//    private static final byte[] VESSEL_YELLOW_MOORED_PNG = {
//        (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, (byte) 0x0d, (byte) 0x0a, (byte) 0x1a, (byte) 0x0a,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x0d, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52,
//        (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x20,
//        (byte) 0x08, (byte) 0x06, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x73, (byte) 0x7a, (byte) 0x7a,
//        (byte) 0xf4, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x6c, (byte) 0x49, (byte) 0x44, (byte) 0x41,
//        (byte) 0x54, (byte) 0x58, (byte) 0xc3, (byte) 0x63, (byte) 0x60, (byte) 0x18, (byte) 0x05, (byte) 0xa3,
//        (byte) 0x60, (byte) 0x14, (byte) 0x8c, (byte) 0x82, (byte) 0x51, (byte) 0x40, (byte) 0x26, (byte) 0x48,
//        (byte) 0x76, (byte) 0xb4, (byte) 0x2e, (byte) 0xbc, (byte) 0xd3, (byte) 0x6a, (byte) 0xfc, (byte) 0x1f,
//        (byte) 0x84, (byte) 0x41, (byte) 0xec, (byte) 0x01, (byte) 0xb1, (byte) 0xfc, (byte) 0xf7, (byte) 0x69,
//        (byte) 0x21, (byte) 0x30, (byte) 0xa6, (byte) 0xab, (byte) 0x23, (byte) 0x90, (byte) 0x2d, (byte) 0xff,
//        (byte) 0xff, (byte) 0x9f, (byte) 0x01, (byte) 0x8c, (byte) 0xe9, (byte) 0xe6, (byte) 0x08, (byte) 0x6c,
//        (byte) 0x96, (byte) 0xd3, (byte) 0xcd, (byte) 0x11, (byte) 0x58, (byte) 0x2d, (byte) 0xbf, (byte) 0x07,
//        (byte) 0xc5, (byte) 0xf4, (byte) 0x70, (byte) 0x04, (byte) 0x86, (byte) 0xe5, (byte) 0x77, (byte) 0x81,
//        (byte) 0x38, (byte) 0x0d, (byte) 0x8a, (byte) 0xef, (byte) 0x62, (byte) 0x3a, (byte) 0x62, (byte) 0xf8,
//        (byte) 0x39, (byte) 0x00, (byte) 0x6b, (byte) 0x14, (byte) 0xdc, (byte) 0xc5, (byte) 0x6e, (byte) 0x39,
//        (byte) 0x7d, (byte) 0xd3, (byte) 0xc1, (byte) 0x60, (byte) 0xc8, (byte) 0x09, (byte) 0x03, (byte) 0x5a,
//        (byte) 0x16, (byte) 0xd0, (byte) 0xdd, (byte) 0xf2, (byte) 0x41, (byte) 0x51, (byte) 0x14, (byte) 0x8f,
//        (byte) 0x82, (byte) 0x51, (byte) 0x30, (byte) 0x0a, (byte) 0x46, (byte) 0xc1, (byte) 0xb0, (byte) 0x02,
//        (byte) 0x00, (byte) 0x42, (byte) 0x04, (byte) 0x04, (byte) 0xfa, (byte) 0x48, (byte) 0x66, (byte) 0x41,
//        (byte) 0xc0, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x00, (byte) 0x49, (byte) 0x45, (byte) 0x4e,
//        (byte) 0x44, (byte) 0xae, (byte) 0x42, (byte) 0x60, (byte) 0x82
//    };
//}

package dk.dma.ais.json_decoder_helpers.enums;

public enum ShipType {
    NOT_AVAILABLE(0), //default
    RESERVED_FOR_FUTURE_USE(1), //1-19
    WING_IN_GROUND_ALL_SHIPS_OF_THIS_TYPE(20),
    WING_IN_GROUND_HAZARDOUS_CATAGORY_A(21),
    WING_IN_GROUND_HAZARDOUS_CATAGORY_B(22),
    WING_IN_GROUND_HAZARDOUS_CATAGORY_C(23),
    WING_IN_GROUND_HAZARDOUS_CATAGORY_D(24),
    WING_IN_GROUND_RESERVED_FOR_FUTURE_USE(25), //25-29
    FISHING(30),
    TOWING(31),
    TOWING_LENGTH_EXCEEDS_200M_OR_BREADTH_EXCEEDS_25M(32),
    DREDGING_OR_UNDERWATER_OPS(33),
    <PERSON>I<PERSON><PERSON><PERSON><PERSON>(34),
    <PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>PS(35),
    <PERSON><PERSON><PERSON>(36),
    <PERSON><PERSON><PERSON><PERSON><PERSON>_CRAFT(37),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(38), //38-39
    <PERSON>IGH_SPEED_CRAFT_ALL_SHIPS_OF_THIS_TYPE(40),
    HIGH_SPEED_CRAFT_HAZARDOUS_CATEGORY_A(41),
    HIGH_SPEED_CRAFT_HAZARDOUS_CATEGORY_B(42),
    HIGH_SPEED_CRAFT_HAZARDOUS_CATEGORY_C(43),
    HIGH_SPEED_CRAFT_HAZARDOUS_CATEGORY_D(44),
    HIGH_SPEED_CRAFT_RESERVED_FOR_FUTURE_USE(45), //45-48
    HIGH_SPEED_CRAFT_NO_ADDITIONAL_INFORMATION(49),
    PILOT_VESSEL(50),
    SEARCH_AND_RESCUE_VESSEL(51),
    TUG(52),
    PORT_TENDER(53),
    ANTI_POLLUTION_EQUIPMENT(54),
    LAW_ENFORCEMENT(55),
    SPARE_LOCAL_VESSEL(56), //56-57
    MEDICAL_TRANSPORT(58),
    NONCOMBATANT_SHIP_ACCORDING_TO_RR_RESOLUTION_NO_18(59),
    PASSENGER_ALL_SHIPS_OF_THIS_TYPE(60),
    PASSENGER_HAZARDOUS_CATEGORY_A(61),
    PASSENGER_HAZARDOUS_CATEGORY_B(62),
    PASSENGER_HAZARDOUS_CATEGORY_C(63),
    PASSENGER_HAZARDOUS_CATEGORY_D(64),
    PASSENGER_RESERVED_FOR_FUTURE_USE(65), //65-68
    PASSENGER_NO_ADDITIONAL_INFORMATION(69),
    CARGO_ALL_SHIPS_OF_THIS_TYPE(70),
    CARGO_HAZARDOUS_CATEGORY_A(71),
    CARGO_HAZARDOUS_CATEGORY_B(72),
    CARGO_HAZARDOUS_CATEGORY_C(73),
    CARGO_HAZARDOUS_CATEGORY_D(74),
    CARGO_RESERVED_FOR_FUTURE_USE(75), //75-78
    CARGO_NO_ADDITIONAL_INFORMATION(79),
    TANKER_ALL_SHIPS_OF_THIS_TYPE(80),
    TANKER_HAZARDOUS_CATEGORY_A(81),
    TANKER_HAZARDOUS_CATEGORY_B(82),
    TANKER_HAZARDOUS_CATEGORY_C(83),
    TANKER_HAZARDOUS_CATEGORY_D(84),
    TANKER_RESERVED_FOR_FUTURE_USE(85), //85-88
    TANKER_NO_ADDITIONAL_INFORMATION(89),
    OTHER_TYPE_ALL_SHIPS_OF_THIS_TYPE(90),
    OTHER_TYPE_HAZARDOUS_CATEGORY_A(91),
    OTHER_TYPE_HAZARDOUS_CATEGORY_B(92),
    OTHER_TYPE_HAZARDOUS_CATEGORY_C(93),
    OTHER_TYPE_HAZARDOUS_CATEGORY_D(94),
    OTHER_TYPE_RESERVED_FOR_FUTURE_USE(95), //95-98
    OTHER_TYPE_NO_ADDITIONAL_INFORMATION(99);

    private final int code;

    ShipType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static ShipType get(int shipTypeCode) {
        for (ShipType shipType : ShipType.values()) {
            if (shipType.getCode() == shipTypeCode) {
                return shipType;
            }
        }
        return NOT_AVAILABLE;
    }

    public String prettyPrint() {
        String shipType =name().replace("_", "-").toLowerCase();
        return shipType.substring(0, 1).toUpperCase() + shipType.substring(1);
    }


    @Override
    public String toString() {
        return prettyPrint();
    }
}

/* Copyright (c) 2011 Danish Maritime Authority.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dk.dma.ais.transform;

import java.util.ArrayList;
import java.util.List;

import dk.dma.ais.packet.AisPacket;

/**
 * The type Packet transformer collection.
 */
public class PacketTransformerCollection implements IAisPacketTransformer {

    private List<IAisPacketTransformer> collection = new ArrayList<>();

    /**
     * Gets collection.
     *
     * @return the collection
     */
    public List<IAisPacketTransformer> getCollection() {
        return collection;
    }

    /**
     * Sets collection.
     *
     * @param collection the collection
     */
    public void setCollection(List<IAisPacketTransformer> collection) {
        this.collection = collection;
    }

    @Override
    public AisPacket transform(AisPacket packet) {
        AisPacket p = packet;
        for (IAisPacketTransformer packetTransformer: getCollection()) {
            p = packetTransformer.transform(p);
        }
        return p;
    }

    /**
     * Add transformer.
     *
     * @param packetTransformer the packet transformer
     */
    public void addTransformer(IAisPacketTransformer packetTransformer) {
        this.collection.add(packetTransformer);
    }

}

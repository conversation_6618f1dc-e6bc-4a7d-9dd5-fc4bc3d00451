/* Copyright (c) 2011 Danish Maritime Authority.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dk.dma.ais.bus;

import java.io.FileNotFoundException;

import dk.dma.ais.configuration.bus.AisBusConfiguration;
import jakarta.xml.bind.JAXBException;

/**
 * Get AisBus instance from XML configuration file
 */
public class AisBusFactory {

    /**
     * Get ais bus.
     *
     * @param filename the filename
     * @return the ais bus
     * @throws JAXBException         the jaxb exception
     * @throws FileNotFoundException the file not found exception
     */
    public static AisBus get(String filename) throws JAXBException, FileNotFoundException {
        return AisBusConfiguration.load(filename).getInstance();
    }

}

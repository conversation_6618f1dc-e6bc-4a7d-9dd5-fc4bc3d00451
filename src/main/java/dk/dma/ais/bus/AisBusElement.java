/* Copyright (c) 2011 Danish Maritime Authority.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package dk.dma.ais.bus;

import dk.dma.ais.packet.AisPacket;

/**
 * An element on the AIS bus with possibilities for further metadata
 */
public final class AisBusElement {

    private final long timestamp;
    private AisPacket packet;

    /**
     * Instantiates a new Ais bus element.
     *
     * @param packet the packet
     */
    public AisBusElement(AisPacket packet) {
        this.packet = packet;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * Gets packet.
     *
     * @return the packet
     */
    public AisPacket getPacket() {
        return packet;
    }

    /**
     * Sets packet.
     *
     * @param packet the packet
     */
    public void setPacket(AisPacket packet) {
        this.packet = packet;
    }

    /**
     * Gets timestamp.
     *
     * @return the timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }

}

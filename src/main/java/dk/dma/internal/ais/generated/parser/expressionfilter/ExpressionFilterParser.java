/* Copyright (c) 2011 Danish Maritime Authority.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Generated from ExpressionFilter.g4 by ANTLR 4.2
package dk.dma.internal.ais.generated.parser.expressionfilter;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.misc.*;
import org.antlr.v4.runtime.tree.*;
import java.util.List;
import java.util.Iterator;
import java.util.ArrayList;

/**
 * The type Expression filter parser.
 */
@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast"})
public class ExpressionFilterParser extends Parser {
    /**
     * The constant _decisionToDFA.
     */
    protected static final DFA[] _decisionToDFA;
    /**
     * The constant _sharedContextCache.
     */
    protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
    /**
     * The constant T__15.
     */
    public static final int
		T__15=1, /**
     * The T __ 14.
     */
    T__14=2, /**
     * The T __ 13.
     */
    T__13=3, /**
     * The T __ 12.
     */
    T__12=4, /**
     * The T __ 11.
     */
    T__11=5, /**
     * The T __ 10.
     */
    T__10=6, /**
     * The T __ 9.
     */
    T__9=7, /**
     * The T __ 8.
     */
    T__8=8,
    /**
     * The T __ 7.
     */
    T__7=9, /**
     * The T __ 6.
     */
    T__6=10, /**
     * The T __ 5.
     */
    T__5=11, /**
     * The T __ 4.
     */
    T__4=12, /**
     * The T __ 3.
     */
    T__3=13, /**
     * The T __ 2.
     */
    T__2=14, /**
     * The T __ 1.
     */
    T__1=15, /**
     * The T __ 0.
     */
    T__0=16,
    /**
     * The And.
     */
    AND=17, /**
     * The Or.
     */
    OR=18, /**
     * The Range.
     */
    RANGE=19, /**
     * The Like.
     */
    LIKE=20, /**
     * The Bbox.
     */
    BBOX=21, /**
     * The Circle.
     */
    CIRCLE=22, /**
     * The Within.
     */
    WITHIN=23, /**
     * The Int.
     */
    INT=24,
    /**
     * The Float.
     */
    FLOAT=25, /**
     * The String.
     */
    STRING=26, /**
     * The Ws.
     */
    WS=27, /**
     * The Prefix source.
     */
    PREFIX_SOURCE=28, /**
     * The Prefix message.
     */
    PREFIX_MESSAGE=29, /**
     * The Prefix target.
     */
    PREFIX_TARGET=30,
    /**
     * The Src id.
     */
    SRC_ID=31, /**
     * The Src basestation.
     */
    SRC_BASESTATION=32, /**
     * The Src country.
     */
    SRC_COUNTRY=33, /**
     * The Src type.
     */
    SRC_TYPE=34, /**
     * The Src region.
     */
    SRC_REGION=35,
    /**
     * The Msg msgid.
     */
    MSG_MSGID=36, /**
     * The Msg mmsi.
     */
    MSG_MMSI=37, /**
     * The Msg imo.
     */
    MSG_IMO=38, /**
     * The Msg type.
     */
    MSG_TYPE=39, /**
     * The Msg country.
     */
    MSG_COUNTRY=40, /**
     * The Msg navstat.
     */
    MSG_NAVSTAT=41,
    /**
     * The Msg name.
     */
    MSG_NAME=42, /**
     * The Msg callsign.
     */
    MSG_CALLSIGN=43, /**
     * The Msg speed.
     */
    MSG_SPEED=44, /**
     * The Msg course.
     */
    MSG_COURSE=45, /**
     * The Msg heading.
     */
    MSG_HEADING=46,
    /**
     * The Msg draught.
     */
    MSG_DRAUGHT=47, /**
     * The Msg latitude.
     */
    MSG_LATITUDE=48, /**
     * The Msg longitude.
     */
    MSG_LONGITUDE=49, /**
     * The Msg position.
     */
    MSG_POSITION=50, /**
     * The Msg time year.
     */
    MSG_TIME_YEAR=51,
    /**
     * The Msg time month.
     */
    MSG_TIME_MONTH=52, /**
     * The Msg time day.
     */
    MSG_TIME_DAY=53, /**
     * The Msg time weekday.
     */
    MSG_TIME_WEEKDAY=54, /**
     * The Msg time hour.
     */
    MSG_TIME_HOUR=55,
    /**
     * The Msg time minute.
     */
    MSG_TIME_MINUTE=56, /**
     * The Tgt imo.
     */
    TGT_IMO=57, /**
     * The Tgt type.
     */
    TGT_TYPE=58, /**
     * The Tgt country.
     */
    TGT_COUNTRY=59, /**
     * The Tgt navstat.
     */
    TGT_NAVSTAT=60,
    /**
     * The Tgt name.
     */
    TGT_NAME=61, /**
     * The Tgt callsign.
     */
    TGT_CALLSIGN=62, /**
     * The Tgt speed.
     */
    TGT_SPEED=63, /**
     * The Tgt course.
     */
    TGT_COURSE=64, /**
     * The Tgt heading.
     */
    TGT_HEADING=65,
    /**
     * The Tgt draught.
     */
    TGT_DRAUGHT=66, /**
     * The Tgt latitude.
     */
    TGT_LATITUDE=67, /**
     * The Tgt longitude.
     */
    TGT_LONGITUDE=68, /**
     * The Tgt position.
     */
    TGT_POSITION=69;
    /**
     * The constant tokenNames.
     */
    public static final String[] tokenNames = {
		"<INVALID>", "'NOT IN'", "'not in'", "'!='", "'messagetype'", "'!@'",
		"'>='", "'<'", "'='", "'>'", "'@'", "'<='", "'IN'", "'in'", "'('", "')'",
		"','", "'&'", "'|'", "'..'", "LIKE", "BBOX", "CIRCLE", "WITHIN", "INT",
		"FLOAT", "STRING", "WS", "'s.'", "'m.'", "'t.'", "SRC_ID", "SRC_BASESTATION",
		"SRC_COUNTRY", "SRC_TYPE", "SRC_REGION", "MSG_MSGID", "MSG_MMSI", "MSG_IMO",
		"MSG_TYPE", "MSG_COUNTRY", "MSG_NAVSTAT", "MSG_NAME", "MSG_CALLSIGN",
		"MSG_SPEED", "MSG_COURSE", "MSG_HEADING", "MSG_DRAUGHT", "MSG_LATITUDE",
		"MSG_LONGITUDE", "MSG_POSITION", "MSG_TIME_YEAR", "MSG_TIME_MONTH", "MSG_TIME_DAY",
		"MSG_TIME_WEEKDAY", "MSG_TIME_HOUR", "MSG_TIME_MINUTE", "TGT_IMO", "TGT_TYPE",
		"TGT_COUNTRY", "TGT_NAVSTAT", "TGT_NAME", "TGT_CALLSIGN", "TGT_SPEED",
		"TGT_COURSE", "TGT_HEADING", "TGT_DRAUGHT", "TGT_LATITUDE", "TGT_LONGITUDE",
		"TGT_POSITION"
	};
    /**
     * The constant RULE_filter.
     */
    public static final int
		RULE_filter = 0, /**
     * The Rule filter expression.
     */
    RULE_filterExpression = 1, /**
     * The Rule compare to.
     */
    RULE_compareTo = 2, /**
     * The Rule in.
     */
    RULE_in = 3,
    /**
     * The Rule notin.
     */
    RULE_notin = 4, /**
     * The Rule int list.
     */
    RULE_intList = 5, /**
     * The Rule string list.
     */
    RULE_stringList = 6, /**
     * The Rule int range.
     */
    RULE_intRange = 7,
    /**
     * The Rule number range.
     */
    RULE_numberRange = 8, /**
     * The Rule number.
     */
    RULE_number = 9, /**
     * The Rule string.
     */
    RULE_string = 10, /**
     * The Rule bbox.
     */
    RULE_bbox = 11,
    /**
     * The Rule circle.
     */
    RULE_circle = 12;
    /**
     * The constant ruleNames.
     */
    public static final String[] ruleNames = {
		"filter", "filterExpression", "compareTo", "in", "notin", "intList", "stringList",
		"intRange", "numberRange", "number", "string", "bbox", "circle"
	};

	@Override
	public String getGrammarFileName() { return "ExpressionFilter.g4"; }

	@Override
	public String[] getTokenNames() { return tokenNames; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public ATN getATN() { return _ATN; }

    /**
     * Instantiates a new Expression filter parser.
     *
     * @param input the input
     */
    public ExpressionFilterParser(TokenStream input) {
		super(input);
		_interp = new ParserATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

    /**
     * The type Filter context.
     */
    public static class FilterContext extends ParserRuleContext {
        /**
         * Filter expression filter expression context.
         *
         * @return the filter expression context
         */
        public FilterExpressionContext filterExpression() {
			return getRuleContext(FilterExpressionContext.class,0);
		}

        /**
         * Eof terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode EOF() { return getToken(ExpressionFilterParser.EOF, 0); }

        /**
         * Instantiates a new Filter context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public FilterContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_filter; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitFilter(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Filter filter context.
     *
     * @return the filter context
     * @throws RecognitionException the recognition exception
     */
    public final FilterContext filter() throws RecognitionException {
		FilterContext _localctx = new FilterContext(_ctx, getState());
		enterRule(_localctx, 0, RULE_filter);
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(26); filterExpression(0);
			setState(27); match(EOF);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type Filter expression context.
     */
    public static class FilterExpressionContext extends ParserRuleContext {
        /**
         * Instantiates a new Filter expression context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public FilterExpressionContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_filterExpression; }

        /**
         * Instantiates a new Filter expression context.
         */
        public FilterExpressionContext() { }

        /**
         * Copy from.
         *
         * @param ctx the ctx
         */
        public void copyFrom(FilterExpressionContext ctx) {
			super.copyFrom(ctx);
		}
	}

    /**
     * The type Message true heading context.
     */
    public static class MessageTrueHeadingContext extends FilterExpressionContext {
        /**
         * Msg heading terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_HEADING() { return getToken(ExpressionFilterParser.MSG_HEADING, 0); }

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message true heading context.
         *
         * @param ctx the ctx
         */
        public MessageTrueHeadingContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTrueHeading(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message true heading in context.
     */
    public static class MessageTrueHeadingInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Msg heading terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_HEADING() { return getToken(ExpressionFilterParser.MSG_HEADING, 0); }

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Message true heading in context.
         *
         * @param ctx the ctx
         */
        public MessageTrueHeadingInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTrueHeadingIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Parens context.
     */
    public static class ParensContext extends FilterExpressionContext {
        /**
         * Filter expression filter expression context.
         *
         * @return the filter expression context
         */
        public FilterExpressionContext filterExpression() {
			return getRuleContext(FilterExpressionContext.class,0);
		}

        /**
         * Instantiates a new Parens context.
         *
         * @param ctx the ctx
         */
        public ParensContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitParens(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time minute context.
     */
    public static class MessageTimeMinuteContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Msg time minute terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_MINUTE() { return getToken(ExpressionFilterParser.MSG_TIME_MINUTE, 0); }

        /**
         * Instantiates a new Message time minute context.
         *
         * @param ctx the ctx
         */
        public MessageTimeMinuteContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeMinute(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time year context.
     */
    public static class MessageTimeYearContext extends FilterExpressionContext {
        /**
         * Msg time year terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_YEAR() { return getToken(ExpressionFilterParser.MSG_TIME_YEAR, 0); }

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message time year context.
         *
         * @param ctx the ctx
         */
        public MessageTimeYearContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeYear(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message navigational status context.
     */
    public static class MessageNavigationalStatusContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Msg navstat terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_NAVSTAT() { return getToken(ExpressionFilterParser.MSG_NAVSTAT, 0); }

        /**
         * Instantiates a new Message navigational status context.
         *
         * @param ctx the ctx
         */
        public MessageNavigationalStatusContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageNavigationalStatus(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time minute in context.
     */
    public static class MessageTimeMinuteInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Msg time minute terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_MINUTE() { return getToken(ExpressionFilterParser.MSG_TIME_MINUTE, 0); }

        /**
         * Instantiates a new Message time minute in context.
         *
         * @param ctx the ctx
         */
        public MessageTimeMinuteInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeMinuteIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Source basestation context.
     */
    public static class SourceBasestationContext extends FilterExpressionContext {
        /**
         * Src basestation terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode SRC_BASESTATION() { return getToken(ExpressionFilterParser.SRC_BASESTATION, 0); }

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Source basestation context.
         *
         * @param ctx the ctx
         */
        public SourceBasestationContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitSourceBasestation(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target navigational status in context.
     */
    public static class TargetNavigationalStatusInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Tgt navstat terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_NAVSTAT() { return getToken(ExpressionFilterParser.TGT_NAVSTAT, 0); }

        /**
         * Instantiates a new Target navigational status in context.
         *
         * @param ctx the ctx
         */
        public TargetNavigationalStatusInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetNavigationalStatusIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target latitude in context.
     */
    public static class TargetLatitudeInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Tgt latitude terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_LATITUDE() { return getToken(ExpressionFilterParser.TGT_LATITUDE, 0); }

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Target latitude in context.
         *
         * @param ctx the ctx
         */
        public TargetLatitudeInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetLatitudeIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target country in context.
     */
    public static class TargetCountryInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Tgt country terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_COUNTRY() { return getToken(ExpressionFilterParser.TGT_COUNTRY, 0); }

        /**
         * Instantiates a new Target country in context.
         *
         * @param ctx the ctx
         */
        public TargetCountryInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetCountryIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target callsign context.
     */
    public static class TargetCallsignContext extends FilterExpressionContext {
        /**
         * Tgt callsign terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_CALLSIGN() { return getToken(ExpressionFilterParser.TGT_CALLSIGN, 0); }

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Like terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode LIKE() { return getToken(ExpressionFilterParser.LIKE, 0); }

        /**
         * Instantiates a new Target callsign context.
         *
         * @param ctx the ctx
         */
        public TargetCallsignContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetCallsign(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time month in context.
     */
    public static class MessageTimeMonthInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Msg time month terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_MONTH() { return getToken(ExpressionFilterParser.MSG_TIME_MONTH, 0); }

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message time month in context.
         *
         * @param ctx the ctx
         */
        public MessageTimeMonthInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeMonthIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Source basestation in context.
     */
    public static class SourceBasestationInContext extends FilterExpressionContext {
        /**
         * Src basestation terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode SRC_BASESTATION() { return getToken(ExpressionFilterParser.SRC_BASESTATION, 0); }

        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Source basestation in context.
         *
         * @param ctx the ctx
         */
        public SourceBasestationInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitSourceBasestationIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message imo in context.
     */
    public static class MessageImoInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Msg imo terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_IMO() { return getToken(ExpressionFilterParser.MSG_IMO, 0); }

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message imo in context.
         *
         * @param ctx the ctx
         */
        public MessageImoInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageImoIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target imo context.
     */
    public static class TargetImoContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Tgt imo terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_IMO() { return getToken(ExpressionFilterParser.TGT_IMO, 0); }

        /**
         * Instantiates a new Target imo context.
         *
         * @param ctx the ctx
         */
        public TargetImoContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetImo(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time weekday context.
     */
    public static class MessageTimeWeekdayContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Msg time weekday terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_WEEKDAY() { return getToken(ExpressionFilterParser.MSG_TIME_WEEKDAY, 0); }

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message time weekday context.
         *
         * @param ctx the ctx
         */
        public MessageTimeWeekdayContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeWeekday(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message country in context.
     */
    public static class MessageCountryInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Msg country terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_COUNTRY() { return getToken(ExpressionFilterParser.MSG_COUNTRY, 0); }

        /**
         * Instantiates a new Message country in context.
         *
         * @param ctx the ctx
         */
        public MessageCountryInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageCountryIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target speed over ground context.
     */
    public static class TargetSpeedOverGroundContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Tgt speed terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_SPEED() { return getToken(ExpressionFilterParser.TGT_SPEED, 0); }

        /**
         * Instantiates a new Target speed over ground context.
         *
         * @param ctx the ctx
         */
        public TargetSpeedOverGroundContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetSpeedOverGround(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target true heading context.
     */
    public static class TargetTrueHeadingContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Tgt heading terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_HEADING() { return getToken(ExpressionFilterParser.TGT_HEADING, 0); }

        /**
         * Instantiates a new Target true heading context.
         *
         * @param ctx the ctx
         */
        public TargetTrueHeadingContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetTrueHeading(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target true heading in context.
     */
    public static class TargetTrueHeadingInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Tgt heading terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_HEADING() { return getToken(ExpressionFilterParser.TGT_HEADING, 0); }

        /**
         * Instantiates a new Target true heading in context.
         *
         * @param ctx the ctx
         */
        public TargetTrueHeadingInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetTrueHeadingIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message latitude in context.
     */
    public static class MessageLatitudeInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Msg latitude terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_LATITUDE() { return getToken(ExpressionFilterParser.MSG_LATITUDE, 0); }

        /**
         * Instantiates a new Message latitude in context.
         *
         * @param ctx the ctx
         */
        public MessageLatitudeInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageLatitudeIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message id context.
     */
    public static class MessageIdContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Msg msgid terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_MSGID() { return getToken(ExpressionFilterParser.MSG_MSGID, 0); }

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message id context.
         *
         * @param ctx the ctx
         */
        public MessageIdContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageId(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message callsign context.
     */
    public static class MessageCallsignContext extends FilterExpressionContext {
        /**
         * Msg callsign terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_CALLSIGN() { return getToken(ExpressionFilterParser.MSG_CALLSIGN, 0); }

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Like terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode LIKE() { return getToken(ExpressionFilterParser.LIKE, 0); }

        /**
         * Instantiates a new Message callsign context.
         *
         * @param ctx the ctx
         */
        public MessageCallsignContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageCallsign(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message speed over ground context.
     */
    public static class MessageSpeedOverGroundContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Msg speed terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_SPEED() { return getToken(ExpressionFilterParser.MSG_SPEED, 0); }

        /**
         * Instantiates a new Message speed over ground context.
         *
         * @param ctx the ctx
         */
        public MessageSpeedOverGroundContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageSpeedOverGround(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target name in context.
     */
    public static class TargetNameInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Tgt name terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_NAME() { return getToken(ExpressionFilterParser.TGT_NAME, 0); }

        /**
         * Instantiates a new Target name in context.
         *
         * @param ctx the ctx
         */
        public TargetNameInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetNameIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message course over ground in context.
     */
    public static class MessageCourseOverGroundInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Msg course terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_COURSE() { return getToken(ExpressionFilterParser.MSG_COURSE, 0); }

        /**
         * Instantiates a new Message course over ground in context.
         *
         * @param ctx the ctx
         */
        public MessageCourseOverGroundInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageCourseOverGroundIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message draught context.
     */
    public static class MessageDraughtContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Msg draught terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_DRAUGHT() { return getToken(ExpressionFilterParser.MSG_DRAUGHT, 0); }

        /**
         * Instantiates a new Message draught context.
         *
         * @param ctx the ctx
         */
        public MessageDraughtContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageDraught(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message mmsi in context.
     */
    public static class MessageMmsiInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Msg mmsi terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_MMSI() { return getToken(ExpressionFilterParser.MSG_MMSI, 0); }

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message mmsi in context.
         *
         * @param ctx the ctx
         */
        public MessageMmsiInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageMmsiIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time hour context.
     */
    public static class MessageTimeHourContext extends FilterExpressionContext {
        /**
         * Msg time hour terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_HOUR() { return getToken(ExpressionFilterParser.MSG_TIME_HOUR, 0); }

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message time hour context.
         *
         * @param ctx the ctx
         */
        public MessageTimeHourContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeHour(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time weekday in context.
     */
    public static class MessageTimeWeekdayInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Msg time weekday terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_WEEKDAY() { return getToken(ExpressionFilterParser.MSG_TIME_WEEKDAY, 0); }

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message time weekday in context.
         *
         * @param ctx the ctx
         */
        public MessageTimeWeekdayInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeWeekdayIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Source country in context.
     */
    public static class SourceCountryInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Src country terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode SRC_COUNTRY() { return getToken(ExpressionFilterParser.SRC_COUNTRY, 0); }

        /**
         * Instantiates a new Source country in context.
         *
         * @param ctx the ctx
         */
        public SourceCountryInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitSourceCountryIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message id in context.
     */
    public static class MessageIdInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Msg msgid terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_MSGID() { return getToken(ExpressionFilterParser.MSG_MSGID, 0); }

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message id in context.
         *
         * @param ctx the ctx
         */
        public MessageIdInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageIdIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target draught context.
     */
    public static class TargetDraughtContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Tgt draught terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_DRAUGHT() { return getToken(ExpressionFilterParser.TGT_DRAUGHT, 0); }

        /**
         * Instantiates a new Target draught context.
         *
         * @param ctx the ctx
         */
        public TargetDraughtContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetDraught(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target imo in context.
     */
    public static class TargetImoInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Tgt imo terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_IMO() { return getToken(ExpressionFilterParser.TGT_IMO, 0); }

        /**
         * Instantiates a new Target imo in context.
         *
         * @param ctx the ctx
         */
        public TargetImoInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetImoIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time year in context.
     */
    public static class MessageTimeYearInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Msg time year terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_YEAR() { return getToken(ExpressionFilterParser.MSG_TIME_YEAR, 0); }

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message time year in context.
         *
         * @param ctx the ctx
         */
        public MessageTimeYearInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeYearIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message name context.
     */
    public static class MessageNameContext extends FilterExpressionContext {
        /**
         * Msg name terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_NAME() { return getToken(ExpressionFilterParser.MSG_NAME, 0); }

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Like terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode LIKE() { return getToken(ExpressionFilterParser.LIKE, 0); }

        /**
         * Instantiates a new Message name context.
         *
         * @param ctx the ctx
         */
        public MessageNameContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageName(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time hour in context.
     */
    public static class MessageTimeHourInContext extends FilterExpressionContext {
        /**
         * Msg time hour terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_HOUR() { return getToken(ExpressionFilterParser.MSG_TIME_HOUR, 0); }

        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message time hour in context.
         *
         * @param ctx the ctx
         */
        public MessageTimeHourInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeHourIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target navigational status context.
     */
    public static class TargetNavigationalStatusContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Tgt navstat terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_NAVSTAT() { return getToken(ExpressionFilterParser.TGT_NAVSTAT, 0); }

        /**
         * Instantiates a new Target navigational status context.
         *
         * @param ctx the ctx
         */
        public TargetNavigationalStatusContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetNavigationalStatus(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target name context.
     */
    public static class TargetNameContext extends FilterExpressionContext {
        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Like terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode LIKE() { return getToken(ExpressionFilterParser.LIKE, 0); }

        /**
         * Tgt name terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_NAME() { return getToken(ExpressionFilterParser.TGT_NAME, 0); }

        /**
         * Instantiates a new Target name context.
         *
         * @param ctx the ctx
         */
        public TargetNameContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetName(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message latitude context.
     */
    public static class MessageLatitudeContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Msg latitude terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_LATITUDE() { return getToken(ExpressionFilterParser.MSG_LATITUDE, 0); }

        /**
         * Instantiates a new Message latitude context.
         *
         * @param ctx the ctx
         */
        public MessageLatitudeContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageLatitude(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message draught in context.
     */
    public static class MessageDraughtInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Msg draught terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_DRAUGHT() { return getToken(ExpressionFilterParser.MSG_DRAUGHT, 0); }

        /**
         * Instantiates a new Message draught in context.
         *
         * @param ctx the ctx
         */
        public MessageDraughtInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageDraughtIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target course over ground in context.
     */
    public static class TargetCourseOverGroundInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Tgt course terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_COURSE() { return getToken(ExpressionFilterParser.TGT_COURSE, 0); }

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Target course over ground in context.
         *
         * @param ctx the ctx
         */
        public TargetCourseOverGroundInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetCourseOverGroundIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time day context.
     */
    public static class MessageTimeDayContext extends FilterExpressionContext {
        /**
         * Msg time day terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_DAY() { return getToken(ExpressionFilterParser.MSG_TIME_DAY, 0); }

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message time day context.
         *
         * @param ctx the ctx
         */
        public MessageTimeDayContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeDay(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message course over ground context.
     */
    public static class MessageCourseOverGroundContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Msg course terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_COURSE() { return getToken(ExpressionFilterParser.MSG_COURSE, 0); }

        /**
         * Instantiates a new Message course over ground context.
         *
         * @param ctx the ctx
         */
        public MessageCourseOverGroundContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageCourseOverGround(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message position inside context.
     */
    public static class MessagePositionInsideContext extends FilterExpressionContext {
        /**
         * Msg position terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_POSITION() { return getToken(ExpressionFilterParser.MSG_POSITION, 0); }

        /**
         * Bbox bbox context.
         *
         * @return the bbox context
         */
        public BboxContext bbox() {
			return getRuleContext(BboxContext.class,0);
		}

        /**
         * Circle circle context.
         *
         * @return the circle context
         */
        public CircleContext circle() {
			return getRuleContext(CircleContext.class,0);
		}

        /**
         * Within terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode WITHIN() { return getToken(ExpressionFilterParser.WITHIN, 0); }

        /**
         * Instantiates a new Message position inside context.
         *
         * @param ctx the ctx
         */
        public MessagePositionInsideContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessagePositionInside(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message shiptype in context.
     */
    public static class MessageShiptypeInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Msg type terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TYPE() { return getToken(ExpressionFilterParser.MSG_TYPE, 0); }

        /**
         * Instantiates a new Message shiptype in context.
         *
         * @param ctx the ctx
         */
        public MessageShiptypeInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageShiptypeIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target course over ground context.
     */
    public static class TargetCourseOverGroundContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Tgt course terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_COURSE() { return getToken(ExpressionFilterParser.TGT_COURSE, 0); }

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Instantiates a new Target course over ground context.
         *
         * @param ctx the ctx
         */
        public TargetCourseOverGroundContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetCourseOverGround(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target position inside context.
     */
    public static class TargetPositionInsideContext extends FilterExpressionContext {
        /**
         * Tgt position terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_POSITION() { return getToken(ExpressionFilterParser.TGT_POSITION, 0); }

        /**
         * Bbox bbox context.
         *
         * @return the bbox context
         */
        public BboxContext bbox() {
			return getRuleContext(BboxContext.class,0);
		}

        /**
         * Circle circle context.
         *
         * @return the circle context
         */
        public CircleContext circle() {
			return getRuleContext(CircleContext.class,0);
		}

        /**
         * Within terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode WITHIN() { return getToken(ExpressionFilterParser.WITHIN, 0); }

        /**
         * Instantiates a new Target position inside context.
         *
         * @param ctx the ctx
         */
        public TargetPositionInsideContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetPositionInside(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message navigational status in context.
     */
    public static class MessageNavigationalStatusInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Msg navstat terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_NAVSTAT() { return getToken(ExpressionFilterParser.MSG_NAVSTAT, 0); }

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message navigational status in context.
         *
         * @param ctx the ctx
         */
        public MessageNavigationalStatusInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageNavigationalStatusIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target speed over ground in context.
     */
    public static class TargetSpeedOverGroundInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Tgt speed terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_SPEED() { return getToken(ExpressionFilterParser.TGT_SPEED, 0); }

        /**
         * Instantiates a new Target speed over ground in context.
         *
         * @param ctx the ctx
         */
        public TargetSpeedOverGroundInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetSpeedOverGroundIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target draught in context.
     */
    public static class TargetDraughtInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Tgt draught terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_DRAUGHT() { return getToken(ExpressionFilterParser.TGT_DRAUGHT, 0); }

        /**
         * Instantiates a new Target draught in context.
         *
         * @param ctx the ctx
         */
        public TargetDraughtInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetDraughtIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Or and context.
     */
    public static class OrAndContext extends FilterExpressionContext {
        /**
         * The Op.
         */
        public Token op;

        /**
         * And terminal node.
         *
         * @param i the
         * @return the terminal node
         */
        public TerminalNode AND(int i) {
			return getToken(ExpressionFilterParser.AND, i);
		}

        /**
         * Filter expression list.
         *
         * @return the list
         */
        public List<FilterExpressionContext> filterExpression() {
			return getRuleContexts(FilterExpressionContext.class);
		}

        /**
         * Filter expression filter expression context.
         *
         * @param i the
         * @return the filter expression context
         */
        public FilterExpressionContext filterExpression(int i) {
			return getRuleContext(FilterExpressionContext.class,i);
		}

        /**
         * And list.
         *
         * @return the list
         */
        public List<TerminalNode> AND() { return getTokens(ExpressionFilterParser.AND); }

        /**
         * Or list.
         *
         * @return the list
         */
        public List<TerminalNode> OR() { return getTokens(ExpressionFilterParser.OR); }

        /**
         * Or terminal node.
         *
         * @param i the
         * @return the terminal node
         */
        public TerminalNode OR(int i) {
			return getToken(ExpressionFilterParser.OR, i);
		}

        /**
         * Instantiates a new Or and context.
         *
         * @param ctx the ctx
         */
        public OrAndContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitOrAnd(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target latitude context.
     */
    public static class TargetLatitudeContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Tgt latitude terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_LATITUDE() { return getToken(ExpressionFilterParser.TGT_LATITUDE, 0); }

        /**
         * Instantiates a new Target latitude context.
         *
         * @param ctx the ctx
         */
        public TargetLatitudeContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetLatitude(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target shiptype in context.
     */
    public static class TargetShiptypeInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Tgt type terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_TYPE() { return getToken(ExpressionFilterParser.TGT_TYPE, 0); }

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Target shiptype in context.
         *
         * @param ctx the ctx
         */
        public TargetShiptypeInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetShiptypeIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time day in context.
     */
    public static class MessageTimeDayInContext extends FilterExpressionContext {
        /**
         * Msg time day terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_DAY() { return getToken(ExpressionFilterParser.MSG_TIME_DAY, 0); }

        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Int range int range context.
         *
         * @return the int range context
         */
        public IntRangeContext intRange() {
			return getRuleContext(IntRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Int list int list context.
         *
         * @return the int list context
         */
        public IntListContext intList() {
			return getRuleContext(IntListContext.class,0);
		}

        /**
         * Instantiates a new Message time day in context.
         *
         * @param ctx the ctx
         */
        public MessageTimeDayInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeDayIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Ais messagetype context.
     */
    public static class AisMessagetypeContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Instantiates a new Ais messagetype context.
         *
         * @param ctx the ctx
         */
        public AisMessagetypeContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitAisMessagetype(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Source id in context.
     */
    public static class SourceIdInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Src id terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode SRC_ID() { return getToken(ExpressionFilterParser.SRC_ID, 0); }

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Source id in context.
         *
         * @param ctx the ctx
         */
        public SourceIdInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitSourceIdIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Source region in context.
     */
    public static class SourceRegionInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Src region terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode SRC_REGION() { return getToken(ExpressionFilterParser.SRC_REGION, 0); }

        /**
         * Instantiates a new Source region in context.
         *
         * @param ctx the ctx
         */
        public SourceRegionInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitSourceRegionIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message callsign in context.
     */
    public static class MessageCallsignInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Msg callsign terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_CALLSIGN() { return getToken(ExpressionFilterParser.MSG_CALLSIGN, 0); }

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Message callsign in context.
         *
         * @param ctx the ctx
         */
        public MessageCallsignInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageCallsignIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message longitude in context.
     */
    public static class MessageLongitudeInContext extends FilterExpressionContext {
        /**
         * Msg longitude terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_LONGITUDE() { return getToken(ExpressionFilterParser.MSG_LONGITUDE, 0); }

        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Message longitude in context.
         *
         * @param ctx the ctx
         */
        public MessageLongitudeInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageLongitudeIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Source type in context.
     */
    public static class SourceTypeInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Src type terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode SRC_TYPE() { return getToken(ExpressionFilterParser.SRC_TYPE, 0); }

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Source type in context.
         *
         * @param ctx the ctx
         */
        public SourceTypeInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitSourceTypeIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message name in context.
     */
    public static class MessageNameInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Msg name terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_NAME() { return getToken(ExpressionFilterParser.MSG_NAME, 0); }

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Message name in context.
         *
         * @param ctx the ctx
         */
        public MessageNameInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageNameIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message speed over ground in context.
     */
    public static class MessageSpeedOverGroundInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Msg speed terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_SPEED() { return getToken(ExpressionFilterParser.MSG_SPEED, 0); }

        /**
         * Instantiates a new Message speed over ground in context.
         *
         * @param ctx the ctx
         */
        public MessageSpeedOverGroundInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageSpeedOverGroundIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message mmsi context.
     */
    public static class MessageMmsiContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Msg mmsi terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_MMSI() { return getToken(ExpressionFilterParser.MSG_MMSI, 0); }

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message mmsi context.
         *
         * @param ctx the ctx
         */
        public MessageMmsiContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageMmsi(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target shiptype context.
     */
    public static class TargetShiptypeContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Tgt type terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_TYPE() { return getToken(ExpressionFilterParser.TGT_TYPE, 0); }

        /**
         * Instantiates a new Target shiptype context.
         *
         * @param ctx the ctx
         */
        public TargetShiptypeContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetShiptype(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message longitude context.
     */
    public static class MessageLongitudeContext extends FilterExpressionContext {
        /**
         * Msg longitude terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_LONGITUDE() { return getToken(ExpressionFilterParser.MSG_LONGITUDE, 0); }

        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Instantiates a new Message longitude context.
         *
         * @param ctx the ctx
         */
        public MessageLongitudeContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageLongitude(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message time month context.
     */
    public static class MessageTimeMonthContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Msg time month terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TIME_MONTH() { return getToken(ExpressionFilterParser.MSG_TIME_MONTH, 0); }

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message time month context.
         *
         * @param ctx the ctx
         */
        public MessageTimeMonthContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageTimeMonth(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target longitude context.
     */
    public static class TargetLongitudeContext extends FilterExpressionContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Tgt longitude terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_LONGITUDE() { return getToken(ExpressionFilterParser.TGT_LONGITUDE, 0); }

        /**
         * Instantiates a new Target longitude context.
         *
         * @param ctx the ctx
         */
        public TargetLongitudeContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetLongitude(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target callsign in context.
     */
    public static class TargetCallsignInContext extends FilterExpressionContext {
        /**
         * Tgt callsign terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_CALLSIGN() { return getToken(ExpressionFilterParser.TGT_CALLSIGN, 0); }

        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * String list string list context.
         *
         * @return the string list context
         */
        public StringListContext stringList() {
			return getRuleContext(StringListContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Instantiates a new Target callsign in context.
         *
         * @param ctx the ctx
         */
        public TargetCallsignInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetCallsignIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message shiptype context.
     */
    public static class MessageShiptypeContext extends FilterExpressionContext {
        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * String string context.
         *
         * @return the string context
         */
        public StringContext string() {
			return getRuleContext(StringContext.class,0);
		}

        /**
         * Msg type terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_TYPE() { return getToken(ExpressionFilterParser.MSG_TYPE, 0); }

        /**
         * Instantiates a new Message shiptype context.
         *
         * @param ctx the ctx
         */
        public MessageShiptypeContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageShiptype(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Message imo context.
     */
    public static class MessageImoContext extends FilterExpressionContext {
        /**
         * Msg imo terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode MSG_IMO() { return getToken(ExpressionFilterParser.MSG_IMO, 0); }

        /**
         * Compare to compare to context.
         *
         * @return the compare to context
         */
        public CompareToContext compareTo() {
			return getRuleContext(CompareToContext.class,0);
		}

        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Instantiates a new Message imo context.
         *
         * @param ctx the ctx
         */
        public MessageImoContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitMessageImo(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * The type Target longitude in context.
     */
    public static class TargetLongitudeInContext extends FilterExpressionContext {
        /**
         * In in context.
         *
         * @return the in context
         */
        public InContext in() {
			return getRuleContext(InContext.class,0);
		}

        /**
         * Number range number range context.
         *
         * @return the number range context
         */
        public NumberRangeContext numberRange() {
			return getRuleContext(NumberRangeContext.class,0);
		}

        /**
         * Notin notin context.
         *
         * @return the notin context
         */
        public NotinContext notin() {
			return getRuleContext(NotinContext.class,0);
		}

        /**
         * Tgt longitude terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode TGT_LONGITUDE() { return getToken(ExpressionFilterParser.TGT_LONGITUDE, 0); }

        /**
         * Instantiates a new Target longitude in context.
         *
         * @param ctx the ctx
         */
        public TargetLongitudeInContext(FilterExpressionContext ctx) { copyFrom(ctx); }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitTargetLongitudeIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Filter expression filter expression context.
     *
     * @return the filter expression context
     * @throws RecognitionException the recognition exception
     */
    public final FilterExpressionContext filterExpression() throws RecognitionException {
		return filterExpression(0);
	}

	private FilterExpressionContext filterExpression(int _p) throws RecognitionException {
		ParserRuleContext _parentctx = _ctx;
		int _parentState = getState();
		FilterExpressionContext _localctx = new FilterExpressionContext(_ctx, _parentState);
		FilterExpressionContext _prevctx = _localctx;
		int _startState = 2;
		enterRecursionRule(_localctx, 2, RULE_filterExpression, _p);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(481);
			switch ( getInterpreter().adaptivePredict(_input,60,_ctx) ) {
			case 1:
				{
				_localctx = new SourceIdInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;

				setState(30); match(SRC_ID);
				setState(33);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(31); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(32); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(35); stringList();
				}
				break;

			case 2:
				{
				_localctx = new SourceBasestationContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(37); match(SRC_BASESTATION);
				setState(38); compareTo();
				setState(39); match(INT);
				}
				break;

			case 3:
				{
				_localctx = new SourceBasestationInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(41); match(SRC_BASESTATION);
				setState(44);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(42); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(43); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(48);
				switch ( getInterpreter().adaptivePredict(_input,2,_ctx) ) {
				case 1:
					{
					setState(46); intRange();
					}
					break;

				case 2:
					{
					setState(47); intList();
					}
					break;
				}
				}
				break;

			case 4:
				{
				_localctx = new SourceCountryInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(50); match(SRC_COUNTRY);
				setState(53);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(51); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(52); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(55); stringList();
				}
				break;

			case 5:
				{
				_localctx = new SourceTypeInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(57); match(SRC_TYPE);
				setState(60);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(58); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(59); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(62); stringList();
				}
				break;

			case 6:
				{
				_localctx = new SourceRegionInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(64); match(SRC_REGION);
				setState(67);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(65); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(66); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(69); stringList();
				}
				break;

			case 7:
				{
				_localctx = new MessageIdContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(71); match(MSG_MSGID);
				setState(72); compareTo();
				setState(73); match(INT);
				}
				break;

			case 8:
				{
				_localctx = new MessageIdInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(75); match(MSG_MSGID);
				setState(78);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(76); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(77); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(82);
				switch ( getInterpreter().adaptivePredict(_input,7,_ctx) ) {
				case 1:
					{
					setState(80); intRange();
					}
					break;

				case 2:
					{
					setState(81); intList();
					}
					break;
				}
				}
				break;

			case 9:
				{
				_localctx = new MessageMmsiContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(84); match(MSG_MMSI);
				setState(85); compareTo();
				setState(86); match(INT);
				}
				break;

			case 10:
				{
				_localctx = new MessageMmsiInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(88); match(MSG_MMSI);
				setState(91);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(89); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(90); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(95);
				switch ( getInterpreter().adaptivePredict(_input,9,_ctx) ) {
				case 1:
					{
					setState(93); intRange();
					}
					break;

				case 2:
					{
					setState(94); intList();
					}
					break;
				}
				}
				break;

			case 11:
				{
				_localctx = new MessageImoContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(97); match(MSG_IMO);
				setState(98); compareTo();
				setState(99); match(INT);
				}
				break;

			case 12:
				{
				_localctx = new MessageImoInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(101); match(MSG_IMO);
				setState(104);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(102); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(103); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(108);
				switch ( getInterpreter().adaptivePredict(_input,11,_ctx) ) {
				case 1:
					{
					setState(106); intRange();
					}
					break;

				case 2:
					{
					setState(107); intList();
					}
					break;
				}
				}
				break;

			case 13:
				{
				_localctx = new MessageShiptypeContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(110); match(MSG_TYPE);
				setState(111); compareTo();
				setState(112); string();
				}
				break;

			case 14:
				{
				_localctx = new MessageShiptypeInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(114); match(MSG_TYPE);
				setState(117);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(115); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(116); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(122);
				switch ( getInterpreter().adaptivePredict(_input,13,_ctx) ) {
				case 1:
					{
					setState(119); intRange();
					}
					break;

				case 2:
					{
					setState(120); intList();
					}
					break;

				case 3:
					{
					setState(121); stringList();
					}
					break;
				}
				}
				break;

			case 15:
				{
				_localctx = new MessageCountryInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(124); match(MSG_COUNTRY);
				setState(127);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(125); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(126); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(129); stringList();
				}
				break;

			case 16:
				{
				_localctx = new MessageNavigationalStatusContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(131); match(MSG_NAVSTAT);
				setState(132); compareTo();
				setState(133); string();
				}
				break;

			case 17:
				{
				_localctx = new MessageNavigationalStatusInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(135); match(MSG_NAVSTAT);
				setState(138);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(136); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(137); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(143);
				switch ( getInterpreter().adaptivePredict(_input,16,_ctx) ) {
				case 1:
					{
					setState(140); intRange();
					}
					break;

				case 2:
					{
					setState(141); intList();
					}
					break;

				case 3:
					{
					setState(142); stringList();
					}
					break;
				}
				}
				break;

			case 18:
				{
				_localctx = new MessageNameContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(145); match(MSG_NAME);
				setState(148);
				switch (_input.LA(1)) {
				case 3:
				case 6:
				case 7:
				case 8:
				case 9:
				case 11:
					{
					setState(146); compareTo();
					}
					break;
				case LIKE:
					{
					setState(147); match(LIKE);
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(150); string();
				}
				break;

			case 19:
				{
				_localctx = new MessageNameInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(151); match(MSG_NAME);
				setState(154);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(152); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(153); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(156); stringList();
				}
				break;

			case 20:
				{
				_localctx = new MessageCallsignContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(158); match(MSG_CALLSIGN);
				setState(161);
				switch (_input.LA(1)) {
				case 3:
				case 6:
				case 7:
				case 8:
				case 9:
				case 11:
					{
					setState(159); compareTo();
					}
					break;
				case LIKE:
					{
					setState(160); match(LIKE);
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(163); string();
				}
				break;

			case 21:
				{
				_localctx = new MessageCallsignInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(164); match(MSG_CALLSIGN);
				setState(167);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(165); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(166); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(169); stringList();
				}
				break;

			case 22:
				{
				_localctx = new MessageSpeedOverGroundContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(171); match(MSG_SPEED);
				setState(172); compareTo();
				setState(173); number();
				}
				break;

			case 23:
				{
				_localctx = new MessageSpeedOverGroundInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(175); match(MSG_SPEED);
				setState(178);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(176); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(177); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(180); numberRange();
				}
				break;

			case 24:
				{
				_localctx = new MessageCourseOverGroundContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(182); match(MSG_COURSE);
				setState(183); compareTo();
				setState(184); number();
				}
				break;

			case 25:
				{
				_localctx = new MessageCourseOverGroundInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(186); match(MSG_COURSE);
				setState(189);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(187); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(188); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(191); numberRange();
				}
				break;

			case 26:
				{
				_localctx = new MessageTrueHeadingContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(193); match(MSG_HEADING);
				setState(194); compareTo();
				setState(195); match(INT);
				}
				break;

			case 27:
				{
				_localctx = new MessageTrueHeadingInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(197); match(MSG_HEADING);
				setState(200);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(198); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(199); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(202); intRange();
				}
				break;

			case 28:
				{
				_localctx = new MessageDraughtContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(204); match(MSG_DRAUGHT);
				setState(205); compareTo();
				setState(206); number();
				}
				break;

			case 29:
				{
				_localctx = new MessageDraughtInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(208); match(MSG_DRAUGHT);
				setState(211);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(209); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(210); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(213); numberRange();
				}
				break;

			case 30:
				{
				_localctx = new MessageLatitudeContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(215); match(MSG_LATITUDE);
				setState(216); compareTo();
				setState(217); number();
				}
				break;

			case 31:
				{
				_localctx = new MessageLatitudeInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(219); match(MSG_LATITUDE);
				setState(222);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(220); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(221); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(224); numberRange();
				}
				break;

			case 32:
				{
				_localctx = new MessageLongitudeContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(226); match(MSG_LONGITUDE);
				setState(227); compareTo();
				setState(228); number();
				}
				break;

			case 33:
				{
				_localctx = new MessageLongitudeInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(230); match(MSG_LONGITUDE);
				setState(233);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(231); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(232); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(235); numberRange();
				}
				break;

			case 34:
				{
				_localctx = new MessagePositionInsideContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(237); match(MSG_POSITION);
				setState(238); match(WITHIN);
				setState(241);
				switch (_input.LA(1)) {
				case CIRCLE:
					{
					setState(239); circle();
					}
					break;
				case BBOX:
					{
					setState(240); bbox();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				}
				break;

			case 35:
				{
				_localctx = new MessageTimeYearContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(243); match(MSG_TIME_YEAR);
				setState(244); compareTo();
				setState(245); match(INT);
				}
				break;

			case 36:
				{
				_localctx = new MessageTimeMonthContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(247); match(MSG_TIME_MONTH);
				setState(248); compareTo();
				setState(251);
				switch ( getInterpreter().adaptivePredict(_input,28,_ctx) ) {
				case 1:
					{
					setState(249); match(INT);
					}
					break;

				case 2:
					{
					setState(250); string();
					}
					break;
				}
				}
				break;

			case 37:
				{
				_localctx = new MessageTimeDayContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(253); match(MSG_TIME_DAY);
				setState(254); compareTo();
				setState(255); match(INT);
				}
				break;

			case 38:
				{
				_localctx = new MessageTimeWeekdayContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(257); match(MSG_TIME_WEEKDAY);
				setState(258); compareTo();
				setState(261);
				switch ( getInterpreter().adaptivePredict(_input,29,_ctx) ) {
				case 1:
					{
					setState(259); match(INT);
					}
					break;

				case 2:
					{
					setState(260); string();
					}
					break;
				}
				}
				break;

			case 39:
				{
				_localctx = new MessageTimeHourContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(263); match(MSG_TIME_HOUR);
				setState(264); compareTo();
				setState(265); match(INT);
				}
				break;

			case 40:
				{
				_localctx = new MessageTimeMinuteContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(267); match(MSG_TIME_MINUTE);
				setState(268); compareTo();
				setState(269); match(INT);
				}
				break;

			case 41:
				{
				_localctx = new MessageTimeYearInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(271); match(MSG_TIME_YEAR);
				setState(274);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(272); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(273); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(278);
				switch ( getInterpreter().adaptivePredict(_input,31,_ctx) ) {
				case 1:
					{
					setState(276); intRange();
					}
					break;

				case 2:
					{
					setState(277); intList();
					}
					break;
				}
				}
				break;

			case 42:
				{
				_localctx = new MessageTimeMonthInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(280); match(MSG_TIME_MONTH);
				setState(283);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(281); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(282); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(288);
				switch ( getInterpreter().adaptivePredict(_input,33,_ctx) ) {
				case 1:
					{
					setState(285); intRange();
					}
					break;

				case 2:
					{
					setState(286); intList();
					}
					break;

				case 3:
					{
					setState(287); stringList();
					}
					break;
				}
				}
				break;

			case 43:
				{
				_localctx = new MessageTimeDayInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(290); match(MSG_TIME_DAY);
				setState(293);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(291); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(292); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(297);
				switch ( getInterpreter().adaptivePredict(_input,35,_ctx) ) {
				case 1:
					{
					setState(295); intRange();
					}
					break;

				case 2:
					{
					setState(296); intList();
					}
					break;
				}
				}
				break;

			case 44:
				{
				_localctx = new MessageTimeWeekdayInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(299); match(MSG_TIME_WEEKDAY);
				setState(302);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(300); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(301); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(307);
				switch ( getInterpreter().adaptivePredict(_input,37,_ctx) ) {
				case 1:
					{
					setState(304); intRange();
					}
					break;

				case 2:
					{
					setState(305); intList();
					}
					break;

				case 3:
					{
					setState(306); stringList();
					}
					break;
				}
				}
				break;

			case 45:
				{
				_localctx = new MessageTimeHourInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(309); match(MSG_TIME_HOUR);
				setState(312);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(310); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(311); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(316);
				switch ( getInterpreter().adaptivePredict(_input,39,_ctx) ) {
				case 1:
					{
					setState(314); intRange();
					}
					break;

				case 2:
					{
					setState(315); intList();
					}
					break;
				}
				}
				break;

			case 46:
				{
				_localctx = new MessageTimeMinuteInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(318); match(MSG_TIME_MINUTE);
				setState(321);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(319); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(320); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(325);
				switch ( getInterpreter().adaptivePredict(_input,41,_ctx) ) {
				case 1:
					{
					setState(323); intRange();
					}
					break;

				case 2:
					{
					setState(324); intList();
					}
					break;
				}
				}
				break;

			case 47:
				{
				_localctx = new TargetImoContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(327); match(TGT_IMO);
				setState(328); compareTo();
				setState(329); match(INT);
				}
				break;

			case 48:
				{
				_localctx = new TargetImoInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(331); match(TGT_IMO);
				setState(334);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(332); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(333); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(338);
				switch ( getInterpreter().adaptivePredict(_input,43,_ctx) ) {
				case 1:
					{
					setState(336); intRange();
					}
					break;

				case 2:
					{
					setState(337); intList();
					}
					break;
				}
				}
				break;

			case 49:
				{
				_localctx = new TargetShiptypeContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(340); match(TGT_TYPE);
				setState(341); compareTo();
				setState(342); string();
				}
				break;

			case 50:
				{
				_localctx = new TargetShiptypeInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(344); match(TGT_TYPE);
				setState(347);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(345); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(346); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(352);
				switch ( getInterpreter().adaptivePredict(_input,45,_ctx) ) {
				case 1:
					{
					setState(349); intRange();
					}
					break;

				case 2:
					{
					setState(350); intList();
					}
					break;

				case 3:
					{
					setState(351); stringList();
					}
					break;
				}
				}
				break;

			case 51:
				{
				_localctx = new TargetCountryInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(354); match(TGT_COUNTRY);
				setState(357);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(355); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(356); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(359); stringList();
				}
				break;

			case 52:
				{
				_localctx = new TargetNavigationalStatusContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(361); match(TGT_NAVSTAT);
				setState(362); compareTo();
				setState(363); string();
				}
				break;

			case 53:
				{
				_localctx = new TargetNavigationalStatusInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(365); match(TGT_NAVSTAT);
				setState(368);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(366); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(367); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(373);
				switch ( getInterpreter().adaptivePredict(_input,48,_ctx) ) {
				case 1:
					{
					setState(370); intRange();
					}
					break;

				case 2:
					{
					setState(371); intList();
					}
					break;

				case 3:
					{
					setState(372); stringList();
					}
					break;
				}
				}
				break;

			case 54:
				{
				_localctx = new TargetNameContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(375); match(TGT_NAME);
				setState(378);
				switch (_input.LA(1)) {
				case 3:
				case 6:
				case 7:
				case 8:
				case 9:
				case 11:
					{
					setState(376); compareTo();
					}
					break;
				case LIKE:
					{
					setState(377); match(LIKE);
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(380); string();
				}
				break;

			case 55:
				{
				_localctx = new TargetNameInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(381); match(TGT_NAME);
				setState(384);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(382); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(383); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(386); stringList();
				}
				break;

			case 56:
				{
				_localctx = new TargetCallsignContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(388); match(TGT_CALLSIGN);
				setState(391);
				switch (_input.LA(1)) {
				case 3:
				case 6:
				case 7:
				case 8:
				case 9:
				case 11:
					{
					setState(389); compareTo();
					}
					break;
				case LIKE:
					{
					setState(390); match(LIKE);
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(393); string();
				}
				break;

			case 57:
				{
				_localctx = new TargetCallsignInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(394); match(TGT_CALLSIGN);
				setState(397);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(395); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(396); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(399); stringList();
				}
				break;

			case 58:
				{
				_localctx = new TargetSpeedOverGroundContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(401); match(TGT_SPEED);
				setState(402); compareTo();
				setState(403); number();
				}
				break;

			case 59:
				{
				_localctx = new TargetSpeedOverGroundInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(405); match(TGT_SPEED);
				setState(408);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(406); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(407); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(410); numberRange();
				}
				break;

			case 60:
				{
				_localctx = new TargetCourseOverGroundContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(412); match(TGT_COURSE);
				setState(413); compareTo();
				setState(414); number();
				}
				break;

			case 61:
				{
				_localctx = new TargetCourseOverGroundInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(416); match(TGT_COURSE);
				setState(419);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(417); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(418); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(421); numberRange();
				}
				break;

			case 62:
				{
				_localctx = new TargetTrueHeadingContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(423); match(TGT_HEADING);
				setState(424); compareTo();
				setState(425); match(INT);
				}
				break;

			case 63:
				{
				_localctx = new TargetTrueHeadingInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(427); match(TGT_HEADING);
				setState(430);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(428); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(429); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(432); intRange();
				}
				break;

			case 64:
				{
				_localctx = new TargetDraughtContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(434); match(TGT_DRAUGHT);
				setState(435); compareTo();
				setState(436); number();
				}
				break;

			case 65:
				{
				_localctx = new TargetDraughtInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(438); match(TGT_DRAUGHT);
				setState(441);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(439); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(440); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(443); numberRange();
				}
				break;

			case 66:
				{
				_localctx = new TargetLatitudeContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(445); match(TGT_LATITUDE);
				setState(446); compareTo();
				setState(447); number();
				}
				break;

			case 67:
				{
				_localctx = new TargetLatitudeInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(449); match(TGT_LATITUDE);
				setState(452);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(450); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(451); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(454); numberRange();
				}
				break;

			case 68:
				{
				_localctx = new TargetLongitudeContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(456); match(TGT_LONGITUDE);
				setState(457); compareTo();
				setState(458); number();
				}
				break;

			case 69:
				{
				_localctx = new TargetLongitudeInContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(460); match(TGT_LONGITUDE);
				setState(463);
				switch (_input.LA(1)) {
				case 8:
				case 10:
				case 12:
				case 13:
					{
					setState(461); in();
					}
					break;
				case 1:
				case 2:
				case 3:
				case 5:
					{
					setState(462); notin();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				setState(465); numberRange();
				}
				break;

			case 70:
				{
				_localctx = new TargetPositionInsideContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(467); match(TGT_POSITION);
				setState(468); match(WITHIN);
				setState(471);
				switch (_input.LA(1)) {
				case CIRCLE:
					{
					setState(469); circle();
					}
					break;
				case BBOX:
					{
					setState(470); bbox();
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				}
				break;

			case 71:
				{
				_localctx = new AisMessagetypeContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(473); match(4);
				setState(474); in();
				setState(475); stringList();
				}
				break;

			case 72:
				{
				_localctx = new ParensContext(_localctx);
				_ctx = _localctx;
				_prevctx = _localctx;
				setState(477); match(14);
				setState(478); filterExpression(0);
				setState(479); match(15);
				}
				break;
			}
			_ctx.stop = _input.LT(-1);
			setState(492);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,62,_ctx);
			while ( _alt!=2 && _alt!=-1 ) {
				if ( _alt==1 ) {
					if ( _parseListeners!=null ) triggerExitRuleEvent();
					_prevctx = _localctx;
					{
					{
					_localctx = new OrAndContext(new FilterExpressionContext(_parentctx, _parentState));
					pushNewRecursionContext(_localctx, _startState, RULE_filterExpression);
					setState(483);
					if (!(precpred(_ctx, 2))) throw new FailedPredicateException(this, "precpred(_ctx, 2)");
					setState(486);
					_errHandler.sync(this);
					_alt = getInterpreter().adaptivePredict(_input,61,_ctx);
					do {
						switch (_alt) {
						case 1:
							{
							{
							setState(484);
							((OrAndContext)_localctx).op = _input.LT(1);
							_la = _input.LA(1);
							if ( !(_la==AND || _la==OR) ) {
								((OrAndContext)_localctx).op = (Token)_errHandler.recoverInline(this);
							}
							consume();
							setState(485); filterExpression(0);
							}
							}
							break;
						default:
							throw new NoViableAltException(this);
						}
						setState(488);
						_errHandler.sync(this);
						_alt = getInterpreter().adaptivePredict(_input,61,_ctx);
					} while ( _alt!=2 && _alt!=-1 );
					}
					}
				}
				setState(494);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,62,_ctx);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			unrollRecursionContexts(_parentctx);
		}
		return _localctx;
	}

    /**
     * The type Compare to context.
     */
    public static class CompareToContext extends ParserRuleContext {
        /**
         * Instantiates a new Compare to context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public CompareToContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_compareTo; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitCompareTo(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Compare to compare to context.
     *
     * @return the compare to context
     * @throws RecognitionException the recognition exception
     */
    public final CompareToContext compareTo() throws RecognitionException {
		CompareToContext _localctx = new CompareToContext(_ctx, getState());
		enterRule(_localctx, 4, RULE_compareTo);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(495);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & ((1L << 3) | (1L << 6) | (1L << 7) | (1L << 8) | (1L << 9) | (1L << 11))) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			consume();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type In context.
     */
    public static class InContext extends ParserRuleContext {
        /**
         * Instantiates a new In context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public InContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_in; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitIn(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * In in context.
     *
     * @return the in context
     * @throws RecognitionException the recognition exception
     */
    public final InContext in() throws RecognitionException {
		InContext _localctx = new InContext(_ctx, getState());
		enterRule(_localctx, 6, RULE_in);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(497);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & ((1L << 8) | (1L << 10) | (1L << 12) | (1L << 13))) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			consume();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type Notin context.
     */
    public static class NotinContext extends ParserRuleContext {
        /**
         * Instantiates a new Notin context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public NotinContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_notin; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitNotin(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Notin notin context.
     *
     * @return the notin context
     * @throws RecognitionException the recognition exception
     */
    public final NotinContext notin() throws RecognitionException {
		NotinContext _localctx = new NotinContext(_ctx, getState());
		enterRule(_localctx, 8, RULE_notin);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(499);
			_la = _input.LA(1);
			if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & ((1L << 1) | (1L << 2) | (1L << 3) | (1L << 5))) != 0)) ) {
			_errHandler.recoverInline(this);
			}
			consume();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type Int list context.
     */
    public static class IntListContext extends ParserRuleContext {
        /**
         * Int terminal node.
         *
         * @param i the
         * @return the terminal node
         */
        public TerminalNode INT(int i) {
			return getToken(ExpressionFilterParser.INT, i);
		}

        /**
         * Int list.
         *
         * @return the list
         */
        public List<TerminalNode> INT() { return getTokens(ExpressionFilterParser.INT); }

        /**
         * Instantiates a new Int list context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public IntListContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_intList; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitIntList(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Int list int list context.
     *
     * @return the int list context
     * @throws RecognitionException the recognition exception
     */
    public final IntListContext intList() throws RecognitionException {
		IntListContext _localctx = new IntListContext(_ctx, getState());
		enterRule(_localctx, 10, RULE_intList);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(502);
			_la = _input.LA(1);
			if (_la==14) {
				{
				setState(501); match(14);
				}
			}

			setState(504); match(INT);
			setState(509);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,64,_ctx);
			while ( _alt!=2 && _alt!=-1 ) {
				if ( _alt==1 ) {
					{
					{
					setState(505); match(16);
					setState(506); match(INT);
					}
					}
				}
				setState(511);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,64,_ctx);
			}
			setState(513);
			switch ( getInterpreter().adaptivePredict(_input,65,_ctx) ) {
			case 1:
				{
				setState(512); match(15);
				}
				break;
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type String list context.
     */
    public static class StringListContext extends ParserRuleContext {
        /**
         * String string context.
         *
         * @param i the
         * @return the string context
         */
        public StringContext string(int i) {
			return getRuleContext(StringContext.class,i);
		}

        /**
         * String list.
         *
         * @return the list
         */
        public List<StringContext> string() {
			return getRuleContexts(StringContext.class);
		}

        /**
         * Instantiates a new String list context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public StringListContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_stringList; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitStringList(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * String list string list context.
     *
     * @return the string list context
     * @throws RecognitionException the recognition exception
     */
    public final StringListContext stringList() throws RecognitionException {
		StringListContext _localctx = new StringListContext(_ctx, getState());
		enterRule(_localctx, 12, RULE_stringList);
		int _la;
		try {
			int _alt;
			enterOuterAlt(_localctx, 1);
			{
			setState(516);
			_la = _input.LA(1);
			if (_la==14) {
				{
				setState(515); match(14);
				}
			}

			setState(518); string();
			setState(523);
			_errHandler.sync(this);
			_alt = getInterpreter().adaptivePredict(_input,67,_ctx);
			while ( _alt!=2 && _alt!=-1 ) {
				if ( _alt==1 ) {
					{
					{
					setState(519); match(16);
					setState(520); string();
					}
					}
				}
				setState(525);
				_errHandler.sync(this);
				_alt = getInterpreter().adaptivePredict(_input,67,_ctx);
			}
			setState(527);
			switch ( getInterpreter().adaptivePredict(_input,68,_ctx) ) {
			case 1:
				{
				setState(526); match(15);
				}
				break;
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type Int range context.
     */
    public static class IntRangeContext extends ParserRuleContext {
        /**
         * Int terminal node.
         *
         * @param i the
         * @return the terminal node
         */
        public TerminalNode INT(int i) {
			return getToken(ExpressionFilterParser.INT, i);
		}

        /**
         * Range terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode RANGE() { return getToken(ExpressionFilterParser.RANGE, 0); }

        /**
         * Int list.
         *
         * @return the list
         */
        public List<TerminalNode> INT() { return getTokens(ExpressionFilterParser.INT); }

        /**
         * Instantiates a new Int range context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public IntRangeContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_intRange; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitIntRange(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Int range int range context.
     *
     * @return the int range context
     * @throws RecognitionException the recognition exception
     */
    public final IntRangeContext intRange() throws RecognitionException {
		IntRangeContext _localctx = new IntRangeContext(_ctx, getState());
		enterRule(_localctx, 14, RULE_intRange);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(530);
			_la = _input.LA(1);
			if (_la==14) {
				{
				setState(529); match(14);
				}
			}

			setState(532); match(INT);
			setState(533); match(RANGE);
			setState(534); match(INT);
			setState(536);
			switch ( getInterpreter().adaptivePredict(_input,70,_ctx) ) {
			case 1:
				{
				setState(535); match(15);
				}
				break;
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type Number range context.
     */
    public static class NumberRangeContext extends ParserRuleContext {
        /**
         * Number list.
         *
         * @return the list
         */
        public List<NumberContext> number() {
			return getRuleContexts(NumberContext.class);
		}

        /**
         * Range terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode RANGE() { return getToken(ExpressionFilterParser.RANGE, 0); }

        /**
         * Number number context.
         *
         * @param i the
         * @return the number context
         */
        public NumberContext number(int i) {
			return getRuleContext(NumberContext.class,i);
		}

        /**
         * Instantiates a new Number range context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public NumberRangeContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_numberRange; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitNumberRange(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Number range number range context.
     *
     * @return the number range context
     * @throws RecognitionException the recognition exception
     */
    public final NumberRangeContext numberRange() throws RecognitionException {
		NumberRangeContext _localctx = new NumberRangeContext(_ctx, getState());
		enterRule(_localctx, 16, RULE_numberRange);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(539);
			_la = _input.LA(1);
			if (_la==14) {
				{
				setState(538); match(14);
				}
			}

			setState(541); number();
			setState(542); match(RANGE);
			setState(543); number();
			setState(545);
			switch ( getInterpreter().adaptivePredict(_input,72,_ctx) ) {
			case 1:
				{
				setState(544); match(15);
				}
				break;
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type Number context.
     */
    public static class NumberContext extends ParserRuleContext {
        /**
         * Int terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode INT() { return getToken(ExpressionFilterParser.INT, 0); }

        /**
         * Float terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode FLOAT() { return getToken(ExpressionFilterParser.FLOAT, 0); }

        /**
         * Instantiates a new Number context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public NumberContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_number; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitNumber(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Number number context.
     *
     * @return the number context
     * @throws RecognitionException the recognition exception
     */
    public final NumberContext number() throws RecognitionException {
		NumberContext _localctx = new NumberContext(_ctx, getState());
		enterRule(_localctx, 18, RULE_number);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(547);
			_la = _input.LA(1);
			if ( !(_la==INT || _la==FLOAT) ) {
			_errHandler.recoverInline(this);
			}
			consume();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type String context.
     */
    public static class StringContext extends ParserRuleContext {
        /**
         * Number number context.
         *
         * @return the number context
         */
        public NumberContext number() {
			return getRuleContext(NumberContext.class,0);
		}

        /**
         * String terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode STRING() { return getToken(ExpressionFilterParser.STRING, 0); }

        /**
         * Instantiates a new String context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public StringContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_string; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitString(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * String string context.
     *
     * @return the string context
     * @throws RecognitionException the recognition exception
     */
    public final StringContext string() throws RecognitionException {
		StringContext _localctx = new StringContext(_ctx, getState());
		enterRule(_localctx, 20, RULE_string);
		try {
			setState(551);
			switch (_input.LA(1)) {
			case INT:
			case FLOAT:
				enterOuterAlt(_localctx, 1);
				{
				setState(549); number();
				}
				break;
			case STRING:
				enterOuterAlt(_localctx, 2);
				{
				setState(550); match(STRING);
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type Bbox context.
     */
    public static class BboxContext extends ParserRuleContext {
        /**
         * Number list.
         *
         * @return the list
         */
        public List<NumberContext> number() {
			return getRuleContexts(NumberContext.class);
		}

        /**
         * Number number context.
         *
         * @param i the
         * @return the number context
         */
        public NumberContext number(int i) {
			return getRuleContext(NumberContext.class,i);
		}

        /**
         * Bbox terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode BBOX() { return getToken(ExpressionFilterParser.BBOX, 0); }

        /**
         * Instantiates a new Bbox context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public BboxContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_bbox; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitBbox(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Bbox bbox context.
     *
     * @return the bbox context
     * @throws RecognitionException the recognition exception
     */
    public final BboxContext bbox() throws RecognitionException {
		BboxContext _localctx = new BboxContext(_ctx, getState());
		enterRule(_localctx, 22, RULE_bbox);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(553); match(BBOX);
			setState(555);
			_la = _input.LA(1);
			if (_la==14) {
				{
				setState(554); match(14);
				}
			}

			setState(557); number();
			setState(558); match(16);
			setState(559); number();
			setState(560); match(16);
			setState(561); number();
			setState(562); match(16);
			setState(563); number();
			setState(565);
			switch ( getInterpreter().adaptivePredict(_input,75,_ctx) ) {
			case 1:
				{
				setState(564); match(15);
				}
				break;
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

    /**
     * The type Circle context.
     */
    public static class CircleContext extends ParserRuleContext {
        /**
         * Number list.
         *
         * @return the list
         */
        public List<NumberContext> number() {
			return getRuleContexts(NumberContext.class);
		}

        /**
         * Number number context.
         *
         * @param i the
         * @return the number context
         */
        public NumberContext number(int i) {
			return getRuleContext(NumberContext.class,i);
		}

        /**
         * Circle terminal node.
         *
         * @return the terminal node
         */
        public TerminalNode CIRCLE() { return getToken(ExpressionFilterParser.CIRCLE, 0); }

        /**
         * Instantiates a new Circle context.
         *
         * @param parent        the parent
         * @param invokingState the invoking state
         */
        public CircleContext(ParserRuleContext parent, int invokingState) {
			super(parent, invokingState);
		}
		@Override public int getRuleIndex() { return RULE_circle; }
		@Override
		public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
			if ( visitor instanceof ExpressionFilterVisitor ) return ((ExpressionFilterVisitor<? extends T>)visitor).visitCircle(this);
			else return visitor.visitChildren(this);
		}
	}

    /**
     * Circle circle context.
     *
     * @return the circle context
     * @throws RecognitionException the recognition exception
     */
    public final CircleContext circle() throws RecognitionException {
		CircleContext _localctx = new CircleContext(_ctx, getState());
		enterRule(_localctx, 24, RULE_circle);
		int _la;
		try {
			enterOuterAlt(_localctx, 1);
			{
			setState(567); match(CIRCLE);
			setState(569);
			_la = _input.LA(1);
			if (_la==14) {
				{
				setState(568); match(14);
				}
			}

			setState(571); number();
			setState(572); match(16);
			setState(573); number();
			setState(574); match(16);
			setState(575); number();
			setState(577);
			switch ( getInterpreter().adaptivePredict(_input,77,_ctx) ) {
			case 1:
				{
				setState(576); match(15);
				}
				break;
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			_errHandler.reportError(this, re);
			_errHandler.recover(this, re);
		}
		finally {
			exitRule();
		}
		return _localctx;
	}

	public boolean sempred(RuleContext _localctx, int ruleIndex, int predIndex) {
		switch (ruleIndex) {
		case 1: return filterExpression_sempred((FilterExpressionContext)_localctx, predIndex);
		}
		return true;
	}
	private boolean filterExpression_sempred(FilterExpressionContext _localctx, int predIndex) {
		switch (predIndex) {
		case 0: return precpred(_ctx, 2);
		}
		return true;
	}

    /**
     * The constant _serializedATN.
     */
    public static final String _serializedATN =
		"\3\u0430\ud6d1\u8206\uad2d\u4417\uaef1\u8d80\uaadd\3G\u0246\4\2\t\2\4"+
		"\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7\t\7\4\b\t\b\4\t\t\t\4\n\t\n\4\13\t"+
		"\13\4\f\t\f\4\r\t\r\4\16\t\16\3\2\3\2\3\2\3\3\3\3\3\3\3\3\5\3$\n\3\3\3"+
		"\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3/\n\3\3\3\3\3\5\3\63\n\3\3\3\3\3\3"+
		"\3\5\38\n\3\3\3\3\3\3\3\3\3\3\3\5\3?\n\3\3\3\3\3\3\3\3\3\3\3\5\3F\n\3"+
		"\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3Q\n\3\3\3\3\3\5\3U\n\3\3\3\3\3"+
		"\3\3\3\3\3\3\3\3\3\3\5\3^\n\3\3\3\3\3\5\3b\n\3\3\3\3\3\3\3\3\3\3\3\3\3"+
		"\3\3\5\3k\n\3\3\3\3\3\5\3o\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3x\n\3\3"+
		"\3\3\3\3\3\5\3}\n\3\3\3\3\3\3\3\5\3\u0082\n\3\3\3\3\3\3\3\3\3\3\3\3\3"+
		"\3\3\3\3\3\3\5\3\u008d\n\3\3\3\3\3\3\3\5\3\u0092\n\3\3\3\3\3\3\3\5\3\u0097"+
		"\n\3\3\3\3\3\3\3\3\3\5\3\u009d\n\3\3\3\3\3\3\3\3\3\3\3\5\3\u00a4\n\3\3"+
		"\3\3\3\3\3\3\3\5\3\u00aa\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u00b5"+
		"\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u00c0\n\3\3\3\3\3\3\3\3\3"+
		"\3\3\3\3\3\3\3\3\3\3\5\3\u00cb\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3"+
		"\5\3\u00d6\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u00e1\n\3\3\3\3"+
		"\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u00ec\n\3\3\3\3\3\3\3\3\3\3\3\3\3\5"+
		"\3\u00f4\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u00fe\n\3\3\3\3\3\3\3"+
		"\3\3\3\3\3\3\3\3\3\3\5\3\u0108\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3"+
		"\3\3\3\3\5\3\u0115\n\3\3\3\3\3\5\3\u0119\n\3\3\3\3\3\3\3\5\3\u011e\n\3"+
		"\3\3\3\3\3\3\5\3\u0123\n\3\3\3\3\3\3\3\5\3\u0128\n\3\3\3\3\3\5\3\u012c"+
		"\n\3\3\3\3\3\3\3\5\3\u0131\n\3\3\3\3\3\3\3\5\3\u0136\n\3\3\3\3\3\3\3\5"+
		"\3\u013b\n\3\3\3\3\3\5\3\u013f\n\3\3\3\3\3\3\3\5\3\u0144\n\3\3\3\3\3\5"+
		"\3\u0148\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u0151\n\3\3\3\3\3\5\3\u0155"+
		"\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u015e\n\3\3\3\3\3\3\3\5\3\u0163\n"+
		"\3\3\3\3\3\3\3\5\3\u0168\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u0173"+
		"\n\3\3\3\3\3\3\3\5\3\u0178\n\3\3\3\3\3\3\3\5\3\u017d\n\3\3\3\3\3\3\3\3"+
		"\3\5\3\u0183\n\3\3\3\3\3\3\3\3\3\3\3\5\3\u018a\n\3\3\3\3\3\3\3\3\3\5\3"+
		"\u0190\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u019b\n\3\3\3\3\3\3"+
		"\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u01a6\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3"+
		"\3\3\3\5\3\u01b1\n\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u01bc\n\3"+
		"\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u01c7\n\3\3\3\3\3\3\3\3\3\3\3"+
		"\3\3\3\3\3\3\3\3\5\3\u01d2\n\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u01da\n\3\3"+
		"\3\3\3\3\3\3\3\3\3\3\3\3\3\3\3\5\3\u01e4\n\3\3\3\3\3\3\3\6\3\u01e9\n\3"+
		"\r\3\16\3\u01ea\7\3\u01ed\n\3\f\3\16\3\u01f0\13\3\3\4\3\4\3\5\3\5\3\6"+
		"\3\6\3\7\5\7\u01f9\n\7\3\7\3\7\3\7\7\7\u01fe\n\7\f\7\16\7\u0201\13\7\3"+
		"\7\5\7\u0204\n\7\3\b\5\b\u0207\n\b\3\b\3\b\3\b\7\b\u020c\n\b\f\b\16\b"+
		"\u020f\13\b\3\b\5\b\u0212\n\b\3\t\5\t\u0215\n\t\3\t\3\t\3\t\3\t\5\t\u021b"+
		"\n\t\3\n\5\n\u021e\n\n\3\n\3\n\3\n\3\n\5\n\u0224\n\n\3\13\3\13\3\f\3\f"+
		"\5\f\u022a\n\f\3\r\3\r\5\r\u022e\n\r\3\r\3\r\3\r\3\r\3\r\3\r\3\r\3\r\5"+
		"\r\u0238\n\r\3\16\3\16\5\16\u023c\n\16\3\16\3\16\3\16\3\16\3\16\3\16\5"+
		"\16\u0244\n\16\3\16\2\3\4\17\2\4\6\b\n\f\16\20\22\24\26\30\32\2\7\3\2"+
		"\23\24\5\2\5\5\b\13\r\r\5\2\n\n\f\f\16\17\4\2\3\5\7\7\3\2\32\33\u02d2"+
		"\2\34\3\2\2\2\4\u01e3\3\2\2\2\6\u01f1\3\2\2\2\b\u01f3\3\2\2\2\n\u01f5"+
		"\3\2\2\2\f\u01f8\3\2\2\2\16\u0206\3\2\2\2\20\u0214\3\2\2\2\22\u021d\3"+
		"\2\2\2\24\u0225\3\2\2\2\26\u0229\3\2\2\2\30\u022b\3\2\2\2\32\u0239\3\2"+
		"\2\2\34\35\5\4\3\2\35\36\7\2\2\3\36\3\3\2\2\2\37 \b\3\1\2 #\7!\2\2!$\5"+
		"\b\5\2\"$\5\n\6\2#!\3\2\2\2#\"\3\2\2\2$%\3\2\2\2%&\5\16\b\2&\u01e4\3\2"+
		"\2\2\'(\7\"\2\2()\5\6\4\2)*\7\32\2\2*\u01e4\3\2\2\2+.\7\"\2\2,/\5\b\5"+
		"\2-/\5\n\6\2.,\3\2\2\2.-\3\2\2\2/\62\3\2\2\2\60\63\5\20\t\2\61\63\5\f"+
		"\7\2\62\60\3\2\2\2\62\61\3\2\2\2\63\u01e4\3\2\2\2\64\67\7#\2\2\658\5\b"+
		"\5\2\668\5\n\6\2\67\65\3\2\2\2\67\66\3\2\2\289\3\2\2\29:\5\16\b\2:\u01e4"+
		"\3\2\2\2;>\7$\2\2<?\5\b\5\2=?\5\n\6\2><\3\2\2\2>=\3\2\2\2?@\3\2\2\2@A"+
		"\5\16\b\2A\u01e4\3\2\2\2BE\7%\2\2CF\5\b\5\2DF\5\n\6\2EC\3\2\2\2ED\3\2"+
		"\2\2FG\3\2\2\2GH\5\16\b\2H\u01e4\3\2\2\2IJ\7&\2\2JK\5\6\4\2KL\7\32\2\2"+
		"L\u01e4\3\2\2\2MP\7&\2\2NQ\5\b\5\2OQ\5\n\6\2PN\3\2\2\2PO\3\2\2\2QT\3\2"+
		"\2\2RU\5\20\t\2SU\5\f\7\2TR\3\2\2\2TS\3\2\2\2U\u01e4\3\2\2\2VW\7\'\2\2"+
		"WX\5\6\4\2XY\7\32\2\2Y\u01e4\3\2\2\2Z]\7\'\2\2[^\5\b\5\2\\^\5\n\6\2]["+
		"\3\2\2\2]\\\3\2\2\2^a\3\2\2\2_b\5\20\t\2`b\5\f\7\2a_\3\2\2\2a`\3\2\2\2"+
		"b\u01e4\3\2\2\2cd\7(\2\2de\5\6\4\2ef\7\32\2\2f\u01e4\3\2\2\2gj\7(\2\2"+
		"hk\5\b\5\2ik\5\n\6\2jh\3\2\2\2ji\3\2\2\2kn\3\2\2\2lo\5\20\t\2mo\5\f\7"+
		"\2nl\3\2\2\2nm\3\2\2\2o\u01e4\3\2\2\2pq\7)\2\2qr\5\6\4\2rs\5\26\f\2s\u01e4"+
		"\3\2\2\2tw\7)\2\2ux\5\b\5\2vx\5\n\6\2wu\3\2\2\2wv\3\2\2\2x|\3\2\2\2y}"+
		"\5\20\t\2z}\5\f\7\2{}\5\16\b\2|y\3\2\2\2|z\3\2\2\2|{\3\2\2\2}\u01e4\3"+
		"\2\2\2~\u0081\7*\2\2\177\u0082\5\b\5\2\u0080\u0082\5\n\6\2\u0081\177\3"+
		"\2\2\2\u0081\u0080\3\2\2\2\u0082\u0083\3\2\2\2\u0083\u0084\5\16\b\2\u0084"+
		"\u01e4\3\2\2\2\u0085\u0086\7+\2\2\u0086\u0087\5\6\4\2\u0087\u0088\5\26"+
		"\f\2\u0088\u01e4\3\2\2\2\u0089\u008c\7+\2\2\u008a\u008d\5\b\5\2\u008b"+
		"\u008d\5\n\6\2\u008c\u008a\3\2\2\2\u008c\u008b\3\2\2\2\u008d\u0091\3\2"+
		"\2\2\u008e\u0092\5\20\t\2\u008f\u0092\5\f\7\2\u0090\u0092\5\16\b\2\u0091"+
		"\u008e\3\2\2\2\u0091\u008f\3\2\2\2\u0091\u0090\3\2\2\2\u0092\u01e4\3\2"+
		"\2\2\u0093\u0096\7,\2\2\u0094\u0097\5\6\4\2\u0095\u0097\7\26\2\2\u0096"+
		"\u0094\3\2\2\2\u0096\u0095\3\2\2\2\u0097\u0098\3\2\2\2\u0098\u01e4\5\26"+
		"\f\2\u0099\u009c\7,\2\2\u009a\u009d\5\b\5\2\u009b\u009d\5\n\6\2\u009c"+
		"\u009a\3\2\2\2\u009c\u009b\3\2\2\2\u009d\u009e\3\2\2\2\u009e\u009f\5\16"+
		"\b\2\u009f\u01e4\3\2\2\2\u00a0\u00a3\7-\2\2\u00a1\u00a4\5\6\4\2\u00a2"+
		"\u00a4\7\26\2\2\u00a3\u00a1\3\2\2\2\u00a3\u00a2\3\2\2\2\u00a4\u00a5\3"+
		"\2\2\2\u00a5\u01e4\5\26\f\2\u00a6\u00a9\7-\2\2\u00a7\u00aa\5\b\5\2\u00a8"+
		"\u00aa\5\n\6\2\u00a9\u00a7\3\2\2\2\u00a9\u00a8\3\2\2\2\u00aa\u00ab\3\2"+
		"\2\2\u00ab\u00ac\5\16\b\2\u00ac\u01e4\3\2\2\2\u00ad\u00ae\7.\2\2\u00ae"+
		"\u00af\5\6\4\2\u00af\u00b0\5\24\13\2\u00b0\u01e4\3\2\2\2\u00b1\u00b4\7"+
		".\2\2\u00b2\u00b5\5\b\5\2\u00b3\u00b5\5\n\6\2\u00b4\u00b2\3\2\2\2\u00b4"+
		"\u00b3\3\2\2\2\u00b5\u00b6\3\2\2\2\u00b6\u00b7\5\22\n\2\u00b7\u01e4\3"+
		"\2\2\2\u00b8\u00b9\7/\2\2\u00b9\u00ba\5\6\4\2\u00ba\u00bb\5\24\13\2\u00bb"+
		"\u01e4\3\2\2\2\u00bc\u00bf\7/\2\2\u00bd\u00c0\5\b\5\2\u00be\u00c0\5\n"+
		"\6\2\u00bf\u00bd\3\2\2\2\u00bf\u00be\3\2\2\2\u00c0\u00c1\3\2\2\2\u00c1"+
		"\u00c2\5\22\n\2\u00c2\u01e4\3\2\2\2\u00c3\u00c4\7\60\2\2\u00c4\u00c5\5"+
		"\6\4\2\u00c5\u00c6\7\32\2\2\u00c6\u01e4\3\2\2\2\u00c7\u00ca\7\60\2\2\u00c8"+
		"\u00cb\5\b\5\2\u00c9\u00cb\5\n\6\2\u00ca\u00c8\3\2\2\2\u00ca\u00c9\3\2"+
		"\2\2\u00cb\u00cc\3\2\2\2\u00cc\u00cd\5\20\t\2\u00cd\u01e4\3\2\2\2\u00ce"+
		"\u00cf\7\61\2\2\u00cf\u00d0\5\6\4\2\u00d0\u00d1\5\24\13\2\u00d1\u01e4"+
		"\3\2\2\2\u00d2\u00d5\7\61\2\2\u00d3\u00d6\5\b\5\2\u00d4\u00d6\5\n\6\2"+
		"\u00d5\u00d3\3\2\2\2\u00d5\u00d4\3\2\2\2\u00d6\u00d7\3\2\2\2\u00d7\u00d8"+
		"\5\22\n\2\u00d8\u01e4\3\2\2\2\u00d9\u00da\7\62\2\2\u00da\u00db\5\6\4\2"+
		"\u00db\u00dc\5\24\13\2\u00dc\u01e4\3\2\2\2\u00dd\u00e0\7\62\2\2\u00de"+
		"\u00e1\5\b\5\2\u00df\u00e1\5\n\6\2\u00e0\u00de\3\2\2\2\u00e0\u00df\3\2"+
		"\2\2\u00e1\u00e2\3\2\2\2\u00e2\u00e3\5\22\n\2\u00e3\u01e4\3\2\2\2\u00e4"+
		"\u00e5\7\63\2\2\u00e5\u00e6\5\6\4\2\u00e6\u00e7\5\24\13\2\u00e7\u01e4"+
		"\3\2\2\2\u00e8\u00eb\7\63\2\2\u00e9\u00ec\5\b\5\2\u00ea\u00ec\5\n\6\2"+
		"\u00eb\u00e9\3\2\2\2\u00eb\u00ea\3\2\2\2\u00ec\u00ed\3\2\2\2\u00ed\u00ee"+
		"\5\22\n\2\u00ee\u01e4\3\2\2\2\u00ef\u00f0\7\64\2\2\u00f0\u00f3\7\31\2"+
		"\2\u00f1\u00f4\5\32\16\2\u00f2\u00f4\5\30\r\2\u00f3\u00f1\3\2\2\2\u00f3"+
		"\u00f2\3\2\2\2\u00f4\u01e4\3\2\2\2\u00f5\u00f6\7\65\2\2\u00f6\u00f7\5"+
		"\6\4\2\u00f7\u00f8\7\32\2\2\u00f8\u01e4\3\2\2\2\u00f9\u00fa\7\66\2\2\u00fa"+
		"\u00fd\5\6\4\2\u00fb\u00fe\7\32\2\2\u00fc\u00fe\5\26\f\2\u00fd\u00fb\3"+
		"\2\2\2\u00fd\u00fc\3\2\2\2\u00fe\u01e4\3\2\2\2\u00ff\u0100\7\67\2\2\u0100"+
		"\u0101\5\6\4\2\u0101\u0102\7\32\2\2\u0102\u01e4\3\2\2\2\u0103\u0104\7"+
		"8\2\2\u0104\u0107\5\6\4\2\u0105\u0108\7\32\2\2\u0106\u0108\5\26\f\2\u0107"+
		"\u0105\3\2\2\2\u0107\u0106\3\2\2\2\u0108\u01e4\3\2\2\2\u0109\u010a\79"+
		"\2\2\u010a\u010b\5\6\4\2\u010b\u010c\7\32\2\2\u010c\u01e4\3\2\2\2\u010d"+
		"\u010e\7:\2\2\u010e\u010f\5\6\4\2\u010f\u0110\7\32\2\2\u0110\u01e4\3\2"+
		"\2\2\u0111\u0114\7\65\2\2\u0112\u0115\5\b\5\2\u0113\u0115\5\n\6\2\u0114"+
		"\u0112\3\2\2\2\u0114\u0113\3\2\2\2\u0115\u0118\3\2\2\2\u0116\u0119\5\20"+
		"\t\2\u0117\u0119\5\f\7\2\u0118\u0116\3\2\2\2\u0118\u0117\3\2\2\2\u0119"+
		"\u01e4\3\2\2\2\u011a\u011d\7\66\2\2\u011b\u011e\5\b\5\2\u011c\u011e\5"+
		"\n\6\2\u011d\u011b\3\2\2\2\u011d\u011c\3\2\2\2\u011e\u0122\3\2\2\2\u011f"+
		"\u0123\5\20\t\2\u0120\u0123\5\f\7\2\u0121\u0123\5\16\b\2\u0122\u011f\3"+
		"\2\2\2\u0122\u0120\3\2\2\2\u0122\u0121\3\2\2\2\u0123\u01e4\3\2\2\2\u0124"+
		"\u0127\7\67\2\2\u0125\u0128\5\b\5\2\u0126\u0128\5\n\6\2\u0127\u0125\3"+
		"\2\2\2\u0127\u0126\3\2\2\2\u0128\u012b\3\2\2\2\u0129\u012c\5\20\t\2\u012a"+
		"\u012c\5\f\7\2\u012b\u0129\3\2\2\2\u012b\u012a\3\2\2\2\u012c\u01e4\3\2"+
		"\2\2\u012d\u0130\78\2\2\u012e\u0131\5\b\5\2\u012f\u0131\5\n\6\2\u0130"+
		"\u012e\3\2\2\2\u0130\u012f\3\2\2\2\u0131\u0135\3\2\2\2\u0132\u0136\5\20"+
		"\t\2\u0133\u0136\5\f\7\2\u0134\u0136\5\16\b\2\u0135\u0132\3\2\2\2\u0135"+
		"\u0133\3\2\2\2\u0135\u0134\3\2\2\2\u0136\u01e4\3\2\2\2\u0137\u013a\79"+
		"\2\2\u0138\u013b\5\b\5\2\u0139\u013b\5\n\6\2\u013a\u0138\3\2\2\2\u013a"+
		"\u0139\3\2\2\2\u013b\u013e\3\2\2\2\u013c\u013f\5\20\t\2\u013d\u013f\5"+
		"\f\7\2\u013e\u013c\3\2\2\2\u013e\u013d\3\2\2\2\u013f\u01e4\3\2\2\2\u0140"+
		"\u0143\7:\2\2\u0141\u0144\5\b\5\2\u0142\u0144\5\n\6\2\u0143\u0141\3\2"+
		"\2\2\u0143\u0142\3\2\2\2\u0144\u0147\3\2\2\2\u0145\u0148\5\20\t\2\u0146"+
		"\u0148\5\f\7\2\u0147\u0145\3\2\2\2\u0147\u0146\3\2\2\2\u0148\u01e4\3\2"+
		"\2\2\u0149\u014a\7;\2\2\u014a\u014b\5\6\4\2\u014b\u014c\7\32\2\2\u014c"+
		"\u01e4\3\2\2\2\u014d\u0150\7;\2\2\u014e\u0151\5\b\5\2\u014f\u0151\5\n"+
		"\6\2\u0150\u014e\3\2\2\2\u0150\u014f\3\2\2\2\u0151\u0154\3\2\2\2\u0152"+
		"\u0155\5\20\t\2\u0153\u0155\5\f\7\2\u0154\u0152\3\2\2\2\u0154\u0153\3"+
		"\2\2\2\u0155\u01e4\3\2\2\2\u0156\u0157\7<\2\2\u0157\u0158\5\6\4\2\u0158"+
		"\u0159\5\26\f\2\u0159\u01e4\3\2\2\2\u015a\u015d\7<\2\2\u015b\u015e\5\b"+
		"\5\2\u015c\u015e\5\n\6\2\u015d\u015b\3\2\2\2\u015d\u015c\3\2\2\2\u015e"+
		"\u0162\3\2\2\2\u015f\u0163\5\20\t\2\u0160\u0163\5\f\7\2\u0161\u0163\5"+
		"\16\b\2\u0162\u015f\3\2\2\2\u0162\u0160\3\2\2\2\u0162\u0161\3\2\2\2\u0163"+
		"\u01e4\3\2\2\2\u0164\u0167\7=\2\2\u0165\u0168\5\b\5\2\u0166\u0168\5\n"+
		"\6\2\u0167\u0165\3\2\2\2\u0167\u0166\3\2\2\2\u0168\u0169\3\2\2\2\u0169"+
		"\u016a\5\16\b\2\u016a\u01e4\3\2\2\2\u016b\u016c\7>\2\2\u016c\u016d\5\6"+
		"\4\2\u016d\u016e\5\26\f\2\u016e\u01e4\3\2\2\2\u016f\u0172\7>\2\2\u0170"+
		"\u0173\5\b\5\2\u0171\u0173\5\n\6\2\u0172\u0170\3\2\2\2\u0172\u0171\3\2"+
		"\2\2\u0173\u0177\3\2\2\2\u0174\u0178\5\20\t\2\u0175\u0178\5\f\7\2\u0176"+
		"\u0178\5\16\b\2\u0177\u0174\3\2\2\2\u0177\u0175\3\2\2\2\u0177\u0176\3"+
		"\2\2\2\u0178\u01e4\3\2\2\2\u0179\u017c\7?\2\2\u017a\u017d\5\6\4\2\u017b"+
		"\u017d\7\26\2\2\u017c\u017a\3\2\2\2\u017c\u017b\3\2\2\2\u017d\u017e\3"+
		"\2\2\2\u017e\u01e4\5\26\f\2\u017f\u0182\7?\2\2\u0180\u0183\5\b\5\2\u0181"+
		"\u0183\5\n\6\2\u0182\u0180\3\2\2\2\u0182\u0181\3\2\2\2\u0183\u0184\3\2"+
		"\2\2\u0184\u0185\5\16\b\2\u0185\u01e4\3\2\2\2\u0186\u0189\7@\2\2\u0187"+
		"\u018a\5\6\4\2\u0188\u018a\7\26\2\2\u0189\u0187\3\2\2\2\u0189\u0188\3"+
		"\2\2\2\u018a\u018b\3\2\2\2\u018b\u01e4\5\26\f\2\u018c\u018f\7@\2\2\u018d"+
		"\u0190\5\b\5\2\u018e\u0190\5\n\6\2\u018f\u018d\3\2\2\2\u018f\u018e\3\2"+
		"\2\2\u0190\u0191\3\2\2\2\u0191\u0192\5\16\b\2\u0192\u01e4\3\2\2\2\u0193"+
		"\u0194\7A\2\2\u0194\u0195\5\6\4\2\u0195\u0196\5\24\13\2\u0196\u01e4\3"+
		"\2\2\2\u0197\u019a\7A\2\2\u0198\u019b\5\b\5\2\u0199\u019b\5\n\6\2\u019a"+
		"\u0198\3\2\2\2\u019a\u0199\3\2\2\2\u019b\u019c\3\2\2\2\u019c\u019d\5\22"+
		"\n\2\u019d\u01e4\3\2\2\2\u019e\u019f\7B\2\2\u019f\u01a0\5\6\4\2\u01a0"+
		"\u01a1\5\24\13\2\u01a1\u01e4\3\2\2\2\u01a2\u01a5\7B\2\2\u01a3\u01a6\5"+
		"\b\5\2\u01a4\u01a6\5\n\6\2\u01a5\u01a3\3\2\2\2\u01a5\u01a4\3\2\2\2\u01a6"+
		"\u01a7\3\2\2\2\u01a7\u01a8\5\22\n\2\u01a8\u01e4\3\2\2\2\u01a9\u01aa\7"+
		"C\2\2\u01aa\u01ab\5\6\4\2\u01ab\u01ac\7\32\2\2\u01ac\u01e4\3\2\2\2\u01ad"+
		"\u01b0\7C\2\2\u01ae\u01b1\5\b\5\2\u01af\u01b1\5\n\6\2\u01b0\u01ae\3\2"+
		"\2\2\u01b0\u01af\3\2\2\2\u01b1\u01b2\3\2\2\2\u01b2\u01b3\5\20\t\2\u01b3"+
		"\u01e4\3\2\2\2\u01b4\u01b5\7D\2\2\u01b5\u01b6\5\6\4\2\u01b6\u01b7\5\24"+
		"\13\2\u01b7\u01e4\3\2\2\2\u01b8\u01bb\7D\2\2\u01b9\u01bc\5\b\5\2\u01ba"+
		"\u01bc\5\n\6\2\u01bb\u01b9\3\2\2\2\u01bb\u01ba\3\2\2\2\u01bc\u01bd\3\2"+
		"\2\2\u01bd\u01be\5\22\n\2\u01be\u01e4\3\2\2\2\u01bf\u01c0\7E\2\2\u01c0"+
		"\u01c1\5\6\4\2\u01c1\u01c2\5\24\13\2\u01c2\u01e4\3\2\2\2\u01c3\u01c6\7"+
		"E\2\2\u01c4\u01c7\5\b\5\2\u01c5\u01c7\5\n\6\2\u01c6\u01c4\3\2\2\2\u01c6"+
		"\u01c5\3\2\2\2\u01c7\u01c8\3\2\2\2\u01c8\u01c9\5\22\n\2\u01c9\u01e4\3"+
		"\2\2\2\u01ca\u01cb\7F\2\2\u01cb\u01cc\5\6\4\2\u01cc\u01cd\5\24\13\2\u01cd"+
		"\u01e4\3\2\2\2\u01ce\u01d1\7F\2\2\u01cf\u01d2\5\b\5\2\u01d0\u01d2\5\n"+
		"\6\2\u01d1\u01cf\3\2\2\2\u01d1\u01d0\3\2\2\2\u01d2\u01d3\3\2\2\2\u01d3"+
		"\u01d4\5\22\n\2\u01d4\u01e4\3\2\2\2\u01d5\u01d6\7G\2\2\u01d6\u01d9\7\31"+
		"\2\2\u01d7\u01da\5\32\16\2\u01d8\u01da\5\30\r\2\u01d9\u01d7\3\2\2\2\u01d9"+
		"\u01d8\3\2\2\2\u01da\u01e4\3\2\2\2\u01db\u01dc\7\6\2\2\u01dc\u01dd\5\b"+
		"\5\2\u01dd\u01de\5\16\b\2\u01de\u01e4\3\2\2\2\u01df\u01e0\7\20\2\2\u01e0"+
		"\u01e1\5\4\3\2\u01e1\u01e2\7\21\2\2\u01e2\u01e4\3\2\2\2\u01e3\37\3\2\2"+
		"\2\u01e3\'\3\2\2\2\u01e3+\3\2\2\2\u01e3\64\3\2\2\2\u01e3;\3\2\2\2\u01e3"+
		"B\3\2\2\2\u01e3I\3\2\2\2\u01e3M\3\2\2\2\u01e3V\3\2\2\2\u01e3Z\3\2\2\2"+
		"\u01e3c\3\2\2\2\u01e3g\3\2\2\2\u01e3p\3\2\2\2\u01e3t\3\2\2\2\u01e3~\3"+
		"\2\2\2\u01e3\u0085\3\2\2\2\u01e3\u0089\3\2\2\2\u01e3\u0093\3\2\2\2\u01e3"+
		"\u0099\3\2\2\2\u01e3\u00a0\3\2\2\2\u01e3\u00a6\3\2\2\2\u01e3\u00ad\3\2"+
		"\2\2\u01e3\u00b1\3\2\2\2\u01e3\u00b8\3\2\2\2\u01e3\u00bc\3\2\2\2\u01e3"+
		"\u00c3\3\2\2\2\u01e3\u00c7\3\2\2\2\u01e3\u00ce\3\2\2\2\u01e3\u00d2\3\2"+
		"\2\2\u01e3\u00d9\3\2\2\2\u01e3\u00dd\3\2\2\2\u01e3\u00e4\3\2\2\2\u01e3"+
		"\u00e8\3\2\2\2\u01e3\u00ef\3\2\2\2\u01e3\u00f5\3\2\2\2\u01e3\u00f9\3\2"+
		"\2\2\u01e3\u00ff\3\2\2\2\u01e3\u0103\3\2\2\2\u01e3\u0109\3\2\2\2\u01e3"+
		"\u010d\3\2\2\2\u01e3\u0111\3\2\2\2\u01e3\u011a\3\2\2\2\u01e3\u0124\3\2"+
		"\2\2\u01e3\u012d\3\2\2\2\u01e3\u0137\3\2\2\2\u01e3\u0140\3\2\2\2\u01e3"+
		"\u0149\3\2\2\2\u01e3\u014d\3\2\2\2\u01e3\u0156\3\2\2\2\u01e3\u015a\3\2"+
		"\2\2\u01e3\u0164\3\2\2\2\u01e3\u016b\3\2\2\2\u01e3\u016f\3\2\2\2\u01e3"+
		"\u0179\3\2\2\2\u01e3\u017f\3\2\2\2\u01e3\u0186\3\2\2\2\u01e3\u018c\3\2"+
		"\2\2\u01e3\u0193\3\2\2\2\u01e3\u0197\3\2\2\2\u01e3\u019e\3\2\2\2\u01e3"+
		"\u01a2\3\2\2\2\u01e3\u01a9\3\2\2\2\u01e3\u01ad\3\2\2\2\u01e3\u01b4\3\2"+
		"\2\2\u01e3\u01b8\3\2\2\2\u01e3\u01bf\3\2\2\2\u01e3\u01c3\3\2\2\2\u01e3"+
		"\u01ca\3\2\2\2\u01e3\u01ce\3\2\2\2\u01e3\u01d5\3\2\2\2\u01e3\u01db\3\2"+
		"\2\2\u01e3\u01df\3\2\2\2\u01e4\u01ee\3\2\2\2\u01e5\u01e8\f\4\2\2\u01e6"+
		"\u01e7\t\2\2\2\u01e7\u01e9\5\4\3\2\u01e8\u01e6\3\2\2\2\u01e9\u01ea\3\2"+
		"\2\2\u01ea\u01e8\3\2\2\2\u01ea\u01eb\3\2\2\2\u01eb\u01ed\3\2\2\2\u01ec"+
		"\u01e5\3\2\2\2\u01ed\u01f0\3\2\2\2\u01ee\u01ec\3\2\2\2\u01ee\u01ef\3\2"+
		"\2\2\u01ef\5\3\2\2\2\u01f0\u01ee\3\2\2\2\u01f1\u01f2\t\3\2\2\u01f2\7\3"+
		"\2\2\2\u01f3\u01f4\t\4\2\2\u01f4\t\3\2\2\2\u01f5\u01f6\t\5\2\2\u01f6\13"+
		"\3\2\2\2\u01f7\u01f9\7\20\2\2\u01f8\u01f7\3\2\2\2\u01f8\u01f9\3\2\2\2"+
		"\u01f9\u01fa\3\2\2\2\u01fa\u01ff\7\32\2\2\u01fb\u01fc\7\22\2\2\u01fc\u01fe"+
		"\7\32\2\2\u01fd\u01fb\3\2\2\2\u01fe\u0201\3\2\2\2\u01ff\u01fd\3\2\2\2"+
		"\u01ff\u0200\3\2\2\2\u0200\u0203\3\2\2\2\u0201\u01ff\3\2\2\2\u0202\u0204"+
		"\7\21\2\2\u0203\u0202\3\2\2\2\u0203\u0204\3\2\2\2\u0204\r\3\2\2\2\u0205"+
		"\u0207\7\20\2\2\u0206\u0205\3\2\2\2\u0206\u0207\3\2\2\2\u0207\u0208\3"+
		"\2\2\2\u0208\u020d\5\26\f\2\u0209\u020a\7\22\2\2\u020a\u020c\5\26\f\2"+
		"\u020b\u0209\3\2\2\2\u020c\u020f\3\2\2\2\u020d\u020b\3\2\2\2\u020d\u020e"+
		"\3\2\2\2\u020e\u0211\3\2\2\2\u020f\u020d\3\2\2\2\u0210\u0212\7\21\2\2"+
		"\u0211\u0210\3\2\2\2\u0211\u0212\3\2\2\2\u0212\17\3\2\2\2\u0213\u0215"+
		"\7\20\2\2\u0214\u0213\3\2\2\2\u0214\u0215\3\2\2\2\u0215\u0216\3\2\2\2"+
		"\u0216\u0217\7\32\2\2\u0217\u0218\7\25\2\2\u0218\u021a\7\32\2\2\u0219"+
		"\u021b\7\21\2\2\u021a\u0219\3\2\2\2\u021a\u021b\3\2\2\2\u021b\21\3\2\2"+
		"\2\u021c\u021e\7\20\2\2\u021d\u021c\3\2\2\2\u021d\u021e\3\2\2\2\u021e"+
		"\u021f\3\2\2\2\u021f\u0220\5\24\13\2\u0220\u0221\7\25\2\2\u0221\u0223"+
		"\5\24\13\2\u0222\u0224\7\21\2\2\u0223\u0222\3\2\2\2\u0223\u0224\3\2\2"+
		"\2\u0224\23\3\2\2\2\u0225\u0226\t\6\2\2\u0226\25\3\2\2\2\u0227\u022a\5"+
		"\24\13\2\u0228\u022a\7\34\2\2\u0229\u0227\3\2\2\2\u0229\u0228\3\2\2\2"+
		"\u022a\27\3\2\2\2\u022b\u022d\7\27\2\2\u022c\u022e\7\20\2\2\u022d\u022c"+
		"\3\2\2\2\u022d\u022e\3\2\2\2\u022e\u022f\3\2\2\2\u022f\u0230\5\24\13\2"+
		"\u0230\u0231\7\22\2\2\u0231\u0232\5\24\13\2\u0232\u0233\7\22\2\2\u0233"+
		"\u0234\5\24\13\2\u0234\u0235\7\22\2\2\u0235\u0237\5\24\13\2\u0236\u0238"+
		"\7\21\2\2\u0237\u0236\3\2\2\2\u0237\u0238\3\2\2\2\u0238\31\3\2\2\2\u0239"+
		"\u023b\7\30\2\2\u023a\u023c\7\20\2\2\u023b\u023a\3\2\2\2\u023b\u023c\3"+
		"\2\2\2\u023c\u023d\3\2\2\2\u023d\u023e\5\24\13\2\u023e\u023f\7\22\2\2"+
		"\u023f\u0240\5\24\13\2\u0240\u0241\7\22\2\2\u0241\u0243\5\24\13\2\u0242"+
		"\u0244\7\21\2\2\u0243\u0242\3\2\2\2\u0243\u0244\3\2\2\2\u0244\33\3\2\2"+
		"\2P#.\62\67>EPT]ajnw|\u0081\u008c\u0091\u0096\u009c\u00a3\u00a9\u00b4"+
		"\u00bf\u00ca\u00d5\u00e0\u00eb\u00f3\u00fd\u0107\u0114\u0118\u011d\u0122"+
		"\u0127\u012b\u0130\u0135\u013a\u013e\u0143\u0147\u0150\u0154\u015d\u0162"+
		"\u0167\u0172\u0177\u017c\u0182\u0189\u018f\u019a\u01a5\u01b0\u01bb\u01c6"+
		"\u01d1\u01d9\u01e3\u01ea\u01ee\u01f8\u01ff\u0203\u0206\u020d\u0211\u0214"+
		"\u021a\u021d\u0223\u0229\u022d\u0237\u023b\u0243";
    /**
     * The constant _ATN.
     */
    public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}